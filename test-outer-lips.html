<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嘴巴宽度和高度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嘴巴宽度和高度绘制测试</h1>
        <p>测试在outer_lips轮廓外侧绘制平行线显示mouth_width和mouth_height</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div class="info">
            <h3>测试说明</h3>
            <p><strong>蓝色虚线:</strong> outer_lips轮廓</p>
            <p><strong>橙色实线:</strong> mouth_width（第48和54点的平行线）</p>
            <p><strong>红色实线:</strong> mouth_height（第51和57点的平行线）</p>
            <p><strong>对应箭头:</strong> 各自线条两端的朝外箭头</p>
            <p><strong>对应文字:</strong> 各自的数值标注</p>
        </div>
    </div>

    <script>
        // 测试数据 - 模拟outer_lips的landmarks
        const testData = {
            outerLipsPoints: [
                { x: 200, y: 200, id: 48 }, // 嘴巴左角 - 宽度测量点1
                { x: 220, y: 190, id: 49 }, // 嘴巴左上
                { x: 240, y: 185, id: 50 }, // 嘴巴上左
                { x: 260, y: 180, id: 51 }, // 嘴巴上中 - 高度测量点1
                { x: 280, y: 185, id: 52 }, // 嘴巴上右
                { x: 300, y: 190, id: 53 }, // 嘴巴右上
                { x: 320, y: 200, id: 54 }, // 嘴巴右角 - 宽度测量点2
                { x: 300, y: 210, id: 55 }, // 嘴巴右下
                { x: 280, y: 215, id: 56 }, // 嘴巴下右
                { x: 260, y: 220, id: 57 }, // 嘴巴下中 - 高度测量点2
                { x: 240, y: 215, id: 58 }, // 嘴巴下左
                { x: 220, y: 210, id: 59 }  // 嘴巴左下
            ],
            mouthWidth: 65,
            mouthHeight: 25
        };

        // 绘制嘴巴宽度的函数（简化版）
        function drawMouthWidth(ctx, outerLipsPoints, mouthWidth) {
            console.log('🎯 开始绘制嘴巴宽度测试');

            // 使用第48和第54个点（id=48和id=54）
            const point48 = outerLipsPoints.find(p => p.id === 48);
            const point54 = outerLipsPoints.find(p => p.id === 54);

            console.log('使用的点:', { point48, point54 });

            // 计算连接线的中点
            const midX = (point48.x + point54.x) / 2;
            const midY = (point48.y + point54.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point54.y - point48.y, point54.x - point48.x);
            
            // 计算平行线的偏移距离（向外偏移，向下偏移）
            const offsetDistance = 30;
            const perpAngle = angle + Math.PI / 2;
            
            // 计算平行线的中点位置（向外偏移，向下偏移）
            const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍长）
            const parallelLength = Math.sqrt(Math.pow(point54.x - point48.x, 2) + Math.pow(point54.y - point48.y, 2)) * 1.2;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point48.x, point48.y);
            ctx.lineTo(point54.x, point54.y);
            ctx.stroke();

            // 绘制平行线（橙色实线）
            ctx.strokeStyle = '#ff9900';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（橙色）
            const arrowSize = 10;
            ctx.strokeStyle = '#ff9900';
            ctx.lineWidth = 2;
            
            // 左箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（橙色背景）
            const text = `${mouthWidth}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(255, 153, 0, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point48, point54].forEach((point) => {
                ctx.fillStyle = '#ff9900';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 嘴巴宽度绘制完成');
        }

        // 绘制嘴巴高度的函数（简化版）
        function drawMouthHeight(ctx, outerLipsPoints, mouthHeight) {
            console.log('🎯 开始绘制嘴巴高度测试');

            // 使用第51和第57个点（id=51和id=57）
            const point51 = outerLipsPoints.find(p => p.id === 51);
            const point57 = outerLipsPoints.find(p => p.id === 57);

            console.log('使用的点:', { point51, point57 });

            // 计算连接线的中点
            const midX = (point51.x + point57.x) / 2;
            const midY = (point51.y + point57.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point57.y - point51.y, point57.x - point51.x);
            
            // 计算平行线的偏移距离（向外偏移，向右偏移）
            const offsetDistance = 35;
            const perpAngle = angle + Math.PI / 2;
            
            // 计算平行线的中点位置（向外偏移，向右偏移）
            const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍长）
            const parallelLength = Math.sqrt(Math.pow(point57.x - point51.x, 2) + Math.pow(point57.y - point51.y, 2)) * 1.2;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point51.x, point51.y);
            ctx.lineTo(point57.x, point57.y);
            ctx.stroke();

            // 绘制平行线（红色实线）
            ctx.strokeStyle = '#cc3300';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（红色）
            const arrowSize = 10;
            ctx.strokeStyle = '#cc3300';
            ctx.lineWidth = 2;
            
            // 上箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 下箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（红色背景）
            const text = `${mouthHeight}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(204, 51, 0, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point51, point57].forEach((point) => {
                ctx.fillStyle = '#cc3300';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 嘴巴高度绘制完成');
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制outer_lips轮廓（蓝色虚线）
            ctx.strokeStyle = '#5F9EA0';
            ctx.lineWidth = 2;
            ctx.setLineDash([6, 4]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(testData.outerLipsPoints[0].x, testData.outerLipsPoints[0].y);
            for (let i = 1; i < testData.outerLipsPoints.length; i++) {
                ctx.lineTo(testData.outerLipsPoints[i].x, testData.outerLipsPoints[i].y);
            }
            ctx.closePath();
            ctx.stroke();

            // 绘制所有outer_lips点
            testData.outerLipsPoints.forEach((point, index) => {
                ctx.fillStyle = '#5F9EA0';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '8px Arial';
                ctx.fillText(`${point.id}`, point.x + 5, point.y - 5);
            });

            // 绘制嘴巴宽度
            drawMouthWidth(ctx, testData.outerLipsPoints, testData.mouthWidth);

            // 绘制嘴巴高度
            drawMouthHeight(ctx, testData.outerLipsPoints, testData.mouthHeight);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
