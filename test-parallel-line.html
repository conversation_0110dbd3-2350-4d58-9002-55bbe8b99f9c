<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平行线测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>平行线绘制测试</h1>
        <p>测试在jaw连接线上方绘制平行线</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div class="info">
            <h3>测试说明</h3>
            <p><strong>红色虚线:</strong> jaw第0点到第16点的连接线</p>
            <p><strong>绿色实线:</strong> 平行线（应该在连接线上方40px）</p>
            <p><strong>蓝色箭头:</strong> 平行线两端的朝外箭头</p>
            <p><strong>黄色文字:</strong> face_weight数值标注</p>
        </div>
    </div>

    <script>
        // 测试数据
        const testData = {
            point0: { x: 100, y: 200 },
            point16: { x: 500, y: 220 },
            faceWeight: 458
        };

        // 绘制面部宽度的函数（简化版）
        function drawFaceWeight(ctx, point0, point16, faceWeight) {
            console.log('🎯 开始绘制面部宽度测试');
            console.log('Point 0:', point0);
            console.log('Point 16:', point16);

            // 计算连接线的中点
            const midX = (point0.x + point16.x) / 2;
            const midY = (point0.y + point16.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point16.y - point0.y, point16.x - point0.x);
            
            // 计算平行线的偏移距离（向上偏移）
            const offsetDistance = 40;
            const perpAngle = angle + Math.PI / 2; // 垂直方向
            
            // 计算平行线的中点位置（向上偏移，在屏幕坐标系中Y轴向下为正，所以向上是负偏移）
            const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍短）
            const parallelLength = Math.sqrt(Math.pow(point16.x - point0.x, 2) + Math.pow(point16.y - point0.y, 2)) * 0.8;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            console.log('📏 计算结果:');
            console.log('  连接线中点:', { x: midX, y: midY });
            console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
            console.log('  平行线起点:', { x: startX, y: startY });
            console.log('  平行线终点:', { x: endX, y: endY });

            // 绘制连接线（红色虚线）
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 2;
            ctx.setLineDash([8, 6]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(point0.x, point0.y);
            ctx.lineTo(point16.x, point16.y);
            ctx.stroke();

            // 绘制平行线（绿色实线）
            ctx.strokeStyle = '#00ff88';
            ctx.lineWidth = 3;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（蓝色）
            const arrowSize = 12;
            ctx.strokeStyle = '#0066ff';
            ctx.lineWidth = 2;
            
            // 左箭头（尖朝左）
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头（尖朝右）
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（黄色背景）
            const text = `${faceWeight}px`;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 16;
            const bgHeight = 24;

            // 背景
            ctx.fillStyle = 'rgba(255, 255, 0, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#000';
            ctx.fillText(text, parallelMidX, parallelMidY + 4);

            // 绘制关键点
            // Point 0
            ctx.fillStyle = '#ff0000';
            ctx.beginPath();
            ctx.arc(point0.x, point0.y, 5, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = '#000';
            ctx.font = '12px Arial';
            ctx.fillText('P0', point0.x + 8, point0.y - 8);

            // Point 16
            ctx.fillStyle = '#ff0000';
            ctx.beginPath();
            ctx.arc(point16.x, point16.y, 5, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = '#000';
            ctx.fillText('P16', point16.x + 8, point16.y - 8);

            console.log('✅ 绘制完成');
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制测试
            drawFaceWeight(ctx, testData.point0, testData.point16, testData.faceWeight);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
