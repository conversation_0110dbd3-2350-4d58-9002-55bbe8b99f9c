// 测试Vue组件集成的脚本
// 模拟detectInfo数据结构

const testDetectInfo = {
  detect: {
    "bounding_box": {"height": 667, "width": 666, "x": 354, "y": 502},
    "face_id": 1,
    "facial_angles": {"chin_angle": 90.18, "eye_angle": -0.58, "mouth_angle": -0.26, "nose_angle": 86.87},
    "facial_measurements": {
      "chin_length": 89,
      "chin_width": 127,
      "eye_inner_width": 168,
      "eye_outer_width": 398,
      "face_length": 458,  // 这是我们要渲染的数据
      "face_width": 574,
      "jaw_width": 246,
      "left_eye_height": 40,
      "left_eye_width": 115,
      "left_eyebrow_height": 11,
      "left_eyebrow_width": 211,
      "lower_face_height": 213,
      "mid_face_height": 245,
      "mouth_height": 63,
      "mouth_width": 220,
      "right_eye_height": 38,
      "right_eye_width": 115,
      "right_eyebrow_height": 9.5,
      "right_eyebrow_width": 200,
      "upper_face_height": 121.5,
      "zygomatic_width": 546
    },
    "landmarks": [
      {"id": 0, "part": "jaw", "x": 391, "y": 646},
      {"id": 1, "part": "jaw", "x": 394, "y": 730},
      {"id": 2, "part": "jaw", "x": 407, "y": 814},
      {"id": 3, "part": "jaw", "x": 425, "y": 895},
      {"id": 4, "part": "jaw", "x": 456, "y": 971},
      {"id": 5, "part": "jaw", "x": 503, "y": 1038},
      {"id": 6, "part": "jaw", "x": 556, "y": 1092},
      {"id": 7, "part": "jaw", "x": 615, "y": 1137},
      {"id": 8, "part": "jaw", "x": 678, "y": 1152},
      {"id": 9, "part": "jaw", "x": 742, "y": 1137},
      {"id": 10, "part": "jaw", "x": 802, "y": 1092},
      {"id": 11, "part": "jaw", "x": 857, "y": 1038},
      {"id": 12, "part": "jaw", "x": 903, "y": 977},
      {"id": 13, "part": "jaw", "x": 933, "y": 905},
      {"id": 14, "part": "jaw", "x": 953, "y": 829},
      {"id": 15, "part": "jaw", "x": 968, "y": 749},
      {"id": 16, "part": "jaw", "x": 974, "y": 667}
      // ... 其他landmarks数据
    ]
  },
  imgWidth: 1000,
  imgHeight: 1200
};

// 验证数据结构
console.log('=== 测试数据验证 ===');
console.log('Face Length:', testDetectInfo.detect.facial_measurements.face_length);
console.log('Jaw Points Count:', testDetectInfo.detect.landmarks.filter(l => l.part === 'jaw').length);
console.log('Point 0:', testDetectInfo.detect.landmarks.find(l => l.id === 0));
console.log('Point 16:', testDetectInfo.detect.landmarks.find(l => l.id === 16));

// 模拟Vue组件中的数据处理逻辑
function simulateVueDataProcessing() {
  console.log('\n=== 模拟Vue组件数据处理 ===');
  
  // 模拟图片尺寸比值计算
  const displayWidth = 375; // 假设的显示宽度
  const displayHeight = 450; // 假设的显示高度
  
  const widthRatio = displayWidth / testDetectInfo.imgWidth;
  const heightRatio = displayHeight / testDetectInfo.imgHeight;
  
  console.log('Width Ratio:', widthRatio);
  console.log('Height Ratio:', heightRatio);
  
  // 模拟landmarks数据处理
  const landmarksObj = {};
  testDetectInfo.detect.landmarks.forEach(landmark => {
    const part = landmark.part;
    if (!landmarksObj[part]) {
      landmarksObj[part] = [];
    }
    landmarksObj[part].push({
      x: landmark.x * widthRatio,
      y: landmark.y * heightRatio,
      originalX: landmark.x,
      originalY: landmark.y,
      id: landmark.id
    });
  });
  
  console.log('Processed Jaw Points:', landmarksObj.jaw?.length);
  console.log('Transformed Point 0:', landmarksObj.jaw?.find(p => p.id === 0));
  console.log('Transformed Point 16:', landmarksObj.jaw?.find(p => p.id === 16));
  
  // 模拟facial_measurements数据保存
  const facialMeasurements = testDetectInfo.detect.facial_measurements;
  console.log('Facial Measurements:', facialMeasurements);
  console.log('Face Length for rendering:', facialMeasurements.face_length);
  
  return {
    landmarksObj,
    facialMeasurements,
    widthRatio,
    heightRatio
  };
}

// 模拟drawFaceLength函数调用
function simulateDrawFaceLength(processedData) {
  console.log('\n=== 模拟drawFaceLength函数调用 ===');
  
  const jawPoints = processedData.landmarksObj.jaw;
  const faceLength = processedData.facialMeasurements.face_length;
  
  if (!jawPoints || jawPoints.length < 17) {
    console.log('❌ Jaw points 数据不足');
    return false;
  }
  
  const point0 = jawPoints.find(p => p.id === 0);
  const point16 = jawPoints.find(p => p.id === 16);
  
  if (!point0 || !point16) {
    console.log('❌ 无法找到指定的jaw points (0和16)');
    return false;
  }
  
  console.log('✅ 找到关键点:');
  console.log('  Point 0:', point0);
  console.log('  Point 16:', point16);
  console.log('  Face Length:', faceLength);
  
  // 计算连接线中点
  const midX = (point0.x + point16.x) / 2;
  const midY = (point0.y + point16.y) / 2;
  console.log('  连接线中点:', { x: midX, y: midY });
  
  // 计算角度
  const angle = Math.atan2(point16.y - point0.y, point16.x - point0.x);
  const perpAngle = angle + Math.PI / 2;
  console.log('  连接线角度:', angle * 180 / Math.PI, '度');
  console.log('  垂直角度:', perpAngle * 180 / Math.PI, '度');
  
  console.log('✅ drawFaceLength函数模拟成功');
  return true;
}

// 运行测试
console.log('开始测试Vue组件集成...\n');

try {
  const processedData = simulateVueDataProcessing();
  const success = simulateDrawFaceLength(processedData);
  
  if (success) {
    console.log('\n🎉 所有测试通过！Vue组件应该能够正确渲染面部长度。');
    console.log('\n📋 功能总结:');
    console.log('1. ✅ 正确解析facial_measurements.face_length数据');
    console.log('2. ✅ 正确处理jaw landmarks数据');
    console.log('3. ✅ 找到第0和第16个关键点');
    console.log('4. ✅ 计算连接线和垂直延长线');
    console.log('5. ✅ 准备渲染距离标注');
  } else {
    console.log('\n❌ 测试失败，请检查数据结构');
  }
} catch (error) {
  console.error('\n💥 测试过程中出现错误:', error);
}

// 输出使用说明
console.log('\n📖 使用说明:');
console.log('1. 确保detectInfo.detect包含facial_measurements.face_length数据');
console.log('2. 确保landmarks中包含jaw部位的17个点（id: 0-16）');
console.log('3. Vue组件会在绘制jaw轮廓时自动调用drawFaceLength函数');
console.log('4. 面部长度会以双箭头+数字的形式显示在jaw轮廓的延长线上');
