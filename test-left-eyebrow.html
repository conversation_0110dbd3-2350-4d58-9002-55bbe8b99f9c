<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左眉宽度和高度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>左眉宽度和高度绘制测试</h1>
        <p>测试在left_eyebrow轮廓外侧绘制平行线和垂直线显示left_eyebrow_width和left_eyebrow_height</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div class="info">
            <h3>测试说明</h3>
            <p><strong>蓝色虚线:</strong> left_eyebrow轮廓</p>
            <p><strong>紫色实线:</strong> left_eyebrow_width（第22和26点的平行线）</p>
            <p><strong>青色实线:</strong> left_eyebrow_height（第24点的垂直线）</p>
            <p><strong>对应箭头:</strong> 各自线条两端的朝外箭头</p>
            <p><strong>对应文字:</strong> 各自的数值标注</p>
        </div>
    </div>

    <script>
        // 测试数据 - 模拟left_eyebrow的landmarks
        const testData = {
            leftEyebrowPoints: [
                { x: 200, y: 120, id: 22 }, // 左眉第1个点 - 宽度测量点1
                { x: 240, y: 110, id: 23 }, // 左眉第2个点
                { x: 280, y: 105, id: 24 }, // 左眉第3个点 - 高度测量点
                { x: 320, y: 110, id: 25 }, // 左眉第4个点
                { x: 360, y: 120, id: 26 }  // 左眉第5个点 - 宽度测量点2
            ],
            leftEyebrowWidth: 211,
            leftEyebrowHeight: 11
        };

        // 绘制左眉宽度的函数（简化版）
        function drawLeftEyebrowWidth(ctx, leftEyebrowPoints, eyebrowWidth) {
            console.log('🎯 开始绘制左眉宽度测试');

            // 使用第22和第26个点（id=22和id=26）
            const point22 = leftEyebrowPoints.find(p => p.id === 22);
            const point26 = leftEyebrowPoints.find(p => p.id === 26);

            console.log('使用的点:', { point22, point26 });

            // 计算连接线的中点
            const midX = (point22.x + point26.x) / 2;
            const midY = (point22.y + point26.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point26.y - point22.y, point26.x - point22.x);
            
            // 计算平行线的偏移距离（向外偏移，向上偏移）
            const offsetDistance = 25;
            const perpAngle = angle + Math.PI / 2;
            
            // 计算平行线的中点位置（向外偏移，向上偏移）
            const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍长）
            const parallelLength = Math.sqrt(Math.pow(point26.x - point22.x, 2) + Math.pow(point26.y - point22.y, 2)) * 1.1;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point22.x, point22.y);
            ctx.lineTo(point26.x, point26.y);
            ctx.stroke();

            // 绘制平行线（紫色实线）
            ctx.strokeStyle = '#ff6bff';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（紫色）
            const arrowSize = 10;
            ctx.strokeStyle = '#ff6bff';
            ctx.lineWidth = 2;
            
            // 左箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（紫色背景）
            const text = `${eyebrowWidth}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(255, 107, 255, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point22, point26].forEach((point) => {
                ctx.fillStyle = '#ff6bff';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 左眉宽度绘制完成');
        }

        // 绘制左眉高度的函数（简化版）
        function drawLeftEyebrowHeight(ctx, leftEyebrowPoints, eyebrowHeight) {
            console.log('🎯 开始绘制左眉高度测试');

            // 使用第22、24和26个点
            const point22 = leftEyebrowPoints.find(p => p.id === 22);
            const point24 = leftEyebrowPoints.find(p => p.id === 24);
            const point26 = leftEyebrowPoints.find(p => p.id === 26);

            console.log('使用的点:', { point22, point24, point26 });

            // 计算22-26连接线的角度
            const baseAngle = Math.atan2(point26.y - point22.y, point26.x - point22.x);
            
            // 计算垂直线的角度（垂直于22-26连接线）
            const perpAngle = baseAngle + Math.PI / 2;
            
            // 计算垂直线的长度和位置
            const verticalLength = 40; // 垂直线的长度
            
            // 从point24开始，向外绘制垂直线
            const startX = point24.x - Math.cos(perpAngle) * (verticalLength / 2);
            const startY = point24.y - Math.sin(perpAngle) * (verticalLength / 2);
            const endX = point24.x + Math.cos(perpAngle) * (verticalLength / 2);
            const endY = point24.y + Math.sin(perpAngle) * (verticalLength / 2);

            // 绘制22-26基准连接线（虚线）
            ctx.strokeStyle = '#6bffff';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.4;
            
            ctx.beginPath();
            ctx.moveTo(point22.x, point22.y);
            ctx.lineTo(point26.x, point26.y);
            ctx.stroke();

            // 绘制垂直线（青色实线）
            ctx.strokeStyle = '#6bffff';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（青色）
            const arrowSize = 10;
            ctx.strokeStyle = '#6bffff';
            ctx.lineWidth = 2;
            
            // 上箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(perpAngle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(perpAngle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(perpAngle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(perpAngle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 下箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(perpAngle + Math.PI/6), endY + arrowSize * Math.sin(perpAngle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(perpAngle - Math.PI/6), endY + arrowSize * Math.sin(perpAngle - Math.PI/6));
            ctx.stroke();

            // 绘制距离标注（在垂直线旁边）
            const textX = point24.x + Math.cos(baseAngle) * 30; // 沿着基准线方向偏移
            const textY = point24.y + Math.sin(baseAngle) * 30;

            // 绘制数值标注（青色背景）
            const text = `${eyebrowHeight}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(107, 255, 255, 0.9)';
            ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, textX, textY + 3);

            // 绘制关键点
            [point22, point24, point26].forEach((point) => {
                ctx.fillStyle = '#6bffff';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 左眉高度绘制完成');
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制left_eyebrow轮廓（蓝色虚线）
            ctx.strokeStyle = '#5F9EA0';
            ctx.lineWidth = 2;
            ctx.setLineDash([6, 4]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(testData.leftEyebrowPoints[0].x, testData.leftEyebrowPoints[0].y);
            for (let i = 1; i < testData.leftEyebrowPoints.length; i++) {
                ctx.lineTo(testData.leftEyebrowPoints[i].x, testData.leftEyebrowPoints[i].y);
            }
            ctx.stroke();

            // 绘制所有left_eyebrow点
            testData.leftEyebrowPoints.forEach((point, index) => {
                ctx.fillStyle = '#5F9EA0';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '8px Arial';
                ctx.fillText(`${point.id}`, point.x + 5, point.y - 5);
            });

            // 绘制左眉宽度
            drawLeftEyebrowWidth(ctx, testData.leftEyebrowPoints, testData.leftEyebrowWidth);

            // 绘制左眉高度
            drawLeftEyebrowHeight(ctx, testData.leftEyebrowPoints, testData.leftEyebrowHeight);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
