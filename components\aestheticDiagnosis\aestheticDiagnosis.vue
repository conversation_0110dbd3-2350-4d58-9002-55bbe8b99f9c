<template>
  <view
    class="w-full max-w-[750rpx] min-h-screen flex flex-col items-center justify-between mx-auto pt-0 ios-safe-area">
    <!-- 上半部分：图片展示 -->

    <view style="transition: all 0.3s;justify-content: center;" class="w-full flex-1 flex flex-col relative topImg">
      <view v-if="!generate.length" class="image-container" style="position: relative; display: inline-block; overflow: hidden;">
        <image class="beforeimg" ref="beforeimgRef" :src="props.peopleImg" mode="widthFix"
          :style="imageStyle" @load="onImageLoad" />
        <canvas v-if="showCanvas" canvas-id="eyeCanvas" id="eyeCanvas" :style="canvasStyle"
          style="position: absolute; top: 0; left: 0; pointer-events: none;"></canvas>
        <!-- 光点动画容器 -->
        <view v-for="(animation, index) in loadingAnimations" :key="animation.id || index" class="light-point" :style="{
          left: animation.x + 'px',
          top: animation.y + 'px',
          opacity: animation.visible ? 1 : 0
        }"></view>
      </view>
      <ImgCompare v-else :before="props.peopleImg" :after="generate" :height="1000" />
    </view>
    <view v-if="!!props.activeReport" class="popup-container"
      :class="[popupVisible ? 'slide-up' : 'slide-down', isExpanded ? 'expanded' : 'collapsed']" @click.stop
      @touchstart.passive @touchmove.passive>
      <!-- <view class="popup-header">
          <text class="popup-title"></text>
          <uni-icons type="closeempty" size="24" color="#666" @click="closePopup"></uni-icons>
        </view> -->

      <view v-if="props.activeReport == '诊断报告'" class=" ">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center ios-button">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 " v-if="'defect_report' in face_aes">
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">下巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.chin.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">轮廓</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.contour.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">眼睛</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.eyes.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">额头</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.forehead.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">嘴巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.mouth.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">鼻子</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.nose.description }}
          </text>
          <view>
            您的总体评分：<view class="ml-4" style="display: inline-flex;">
              {{ face_aes.overall_score }}
            </view>
          </view>
        </view>
      </view>
      <template v-if="props.activeReport == '美学方案'">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center ios-button">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 ">
          <template v-for="item in face_aes.treatment_plans.plan_a.projects" :key="item.name">
            <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">{{ item.name }}</text>
            <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
              {{ item.reason }}
            </text>
          </template>
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">总结：</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.treatment_plans.plan_a.reason }}
          </text>
        </view>
      </template>

      <scroll-view v-if="props.activeReport == '专属推荐'" class="popup-content" scroll-x style="white-space: nowrap;">
        <view class="flex flex-nowrap" style="width: max-content;">
          <view class="flex items-center mx-2 p-2 bg-[#F8F8F8]" v-for="(item, index) in recommendedItems"
            @click="gotoOrgList" :key="index">
            <div class="flex flex-col flex-1">
              <div class="text-[14px] text-[#222] leading-tight" style="font-family: 'PingFang SC', Arial;">{{
                item.title }}</div>
              <div class="text-[12px] text-[#999] mt-1" style="font-family: 'PingFang SC', Arial;">{{
                item.category }}
                <span>{{ item.location }}</span>
              </div>
            </div>

            <img :src="item.imgSrc" :alt="item.alt" class="w-14 h-14 rounded-lg object-cover ml-3" />
          </view>
        </view>
      </scroll-view>

    </view>




  </view>
  <!-- 底部导航栏2 -->
  <view v-show="props.activeReport" style="z-index: 5;"
    class="fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform ios-bottom-safe"
    :class="{ 'translate-y-0': props.activeReport, 'translate-y-full': !props.activeReport }">
    <view class="flex justify-around items-center h-16 px-4">
      <view v-for="item in reports" style="font-size: 28rpx;border-color: #F39196;" class="font-semi"
        :class="props.activeReport == item ? 'font-semibold border-b-2' : ''" @click="selectIcon2(item)">{{ item
        }}</view>
    </view>
  </view>

</template>

<script setup>
import { useStore } from "vuex"
import ImgCompare from '@/components/imgCompare.vue';
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { callAiPolling } from "@/Api/index.js"
const props = defineProps({
  compareFlag: {
    type: Boolean,
    default: false
  },
  peopleImg: {
    type: String,
    default: ''
  },

  buttonIndex: {
    type: Number,
    default: -1
  },
  activeReport: {
    type: String,
    default: ''
  },
  icons: {
    type: Array,
    default: []
  },
  reports: {
    type: Array,
    default: []
  },

});
let timer = null

// 图片加载完成事件处理
const onImageLoad = () => {
  // 图片加载完成后处理detectInfo
  let detectInfo = uni.getStorageSync('detectInfo')
  if (detectInfo && detectInfo.detect) {
    detectInfo.detect = JSON.parse(detectInfo.detect).faces[0]
    console.log(detectInfo.detect);
    console.log(JSON.stringify(detectInfo.detect));
    
    // 计算图片宽度比值和处理landmarks
    processDetectInfo(detectInfo)
  }
}

onMounted(() => {

  // emit('update:loading', true)
  // iOS兼容性：初始化状态
  isExpanded.value = false
  isAnimating.value = false
  popupVisible.value = false

  let label = props.icons[0].reports[0];
  selectIcon2(label);
  let operationId = uni.getStorageSync('operationId')
  if (operationId) {
    getStatus(operationId)

    timer = setInterval(async () => {
      getStatus(operationId)
    }, 3000)
  }
})
onBeforeUnmount(() => {

  clearInterval(timer)
})
const emit = defineEmits(['update:activeReport', 'update:loading', 'update:percent', 'show-login']);

const beforeImgHeight = ref(0) // 原图高度
const beforeimgRef = ref(null) // 图片引用
const isExpanded = ref(false) // 控制弹窗展开状态
const isAnimating = ref(false) // 控制动画状态，防止iOS端快速点击

// 处理detectInfo数据
const processDetectInfo = (detectInfo) => {
  // 使用nextTick确保DOM已渲染，然后获取图片实际尺寸
  nextTick(() => {
    setTimeout(() => {
      // 获取图片元素的实际显示尺寸
      const query = uni.createSelectorQuery()
      query.select('.beforeimg').boundingClientRect((rect) => {

        if (rect) {
          const displayWidth = rect.width
          const displayHeight = rect.height

          // 计算宽高比值
          const widthRatio = displayWidth / detectInfo.imgWidth
          const heightRatio = displayHeight / detectInfo.imgHeight

          // 处理landmarks数据，按part分组，并对坐标进行比值计算
          const landmarksObj = {}
          if (detectInfo.detect && detectInfo.detect.landmarks) {
            detectInfo.detect.landmarks.forEach(landmark => {
              const part = landmark.part
              if (!landmarksObj[part]) {
                landmarksObj[part] = []
              }
              landmarksObj[part].push({
                x: landmark.x * widthRatio,  // 对x坐标进行宽度比值计算
                y: landmark.y * heightRatio, // 对y坐标进行高度比值计算
                originalX: landmark.x,       // 保留原始x坐标
                originalY: landmark.y,       // 保留原始y坐标
                id: landmark.id
              })
            })
          }


          // 将数据存储到响应式变量中供后续使用
          imageWidthRatio.value = widthRatio
          imageHeightRatio.value = heightRatio
          landmarksData.value = landmarksObj

          // 保存facial_angles数据
          if (detectInfo.detect && detectInfo.detect.facial_angles) {
            facialAngles.value = detectInfo.detect.facial_angles
            console.log('保存的facial_angles数据:', facialAngles.value);
            console.log('chin_angle:', facialAngles.value.chin_angle);
          }

          // 保存facial_measurements数据
          if (detectInfo.detect && detectInfo.detect.facial_measurements) {
            facialMeasurements.value = detectInfo.detect.facial_measurements
            console.log('保存的facial_measurements数据:', facialMeasurements.value);

            // 检查face_width或face_length字段
            const faceWeight = facialMeasurements.value.face_width || facialMeasurements.value.face_length;
            console.log('face_width:', facialMeasurements.value.face_width);
            console.log('face_length:', facialMeasurements.value.face_length);
            console.log('使用的数值:', faceWeight);

            // 如果没有face_width但有face_length，则复制过来
            if (!facialMeasurements.value.face_width && facialMeasurements.value.face_length) {
              facialMeasurements.value.face_width = facialMeasurements.value.face_length;
              console.log('已将face_length复制为face_width:', facialMeasurements.value.face_width);
            }

            // 检查left_eye_height和left_eye_width字段
            console.log('left_eye_height:', facialMeasurements.value.left_eye_height);
            console.log('left_eye_width:', facialMeasurements.value.left_eye_width);

            // 检查right_eye_height和right_eye_width字段
            console.log('right_eye_height:', facialMeasurements.value.right_eye_height);
            console.log('right_eye_width:', facialMeasurements.value.right_eye_width);

            // 检查left_eyebrow_width和left_eyebrow_height字段
            console.log('left_eyebrow_width:', facialMeasurements.value.left_eyebrow_width);
            console.log('left_eyebrow_height:', facialMeasurements.value.left_eyebrow_height);
          }

          // 初始化canvas绘制
          initCanvasDrawing(displayWidth, displayHeight, landmarksObj)
        } else {
          // console.error('无法获取图片元素尺寸')
        }
      }).exec()
    }, 1000);
  })
}

// 响应式变量存储处理后的数据
const imageWidthRatio = ref(0)
const imageHeightRatio = ref(0)
const landmarksData = ref({})
const facialAngles = ref({})
const facialMeasurements = ref({})

// Canvas相关变量
const showCanvas = ref(false)
const canvasStyle = ref({})
const imageStyle = ref({ width: '100%' })
const loadingAnimations = ref([])
const animationFrames = ref([])

// Canvas绘制和动画相关方法

// 计算路径总长度
const calculatePathLength = (points) => {
  let length = 0;
  for (let i = 0; i < points.length - 1; i++) {
    const dx = points[i + 1].x - points[i].x;
    const dy = points[i + 1].y - points[i].y;
    length += Math.sqrt(dx * dx + dy * dy);
  }
  // 闭合路径
  const dx = points[0].x - points[points.length - 1].x;
  const dy = points[0].y - points[points.length - 1].y;
  length += Math.sqrt(dx * dx + dy * dy);
  return length;
}

// 根据路径长度比例获取对应点
const getPointAtLength = (points, pathLength, targetLength) => {
  let accumulatedLength = 0;

  // 检查闭合路径的最后一段
  const lastSegmentLength = Math.sqrt(
    Math.pow(points[0].x - points[points.length - 1].x, 2) +
    Math.pow(points[0].y - points[points.length - 1].y, 2)
  );

  // 先检查是否在最后一段
  if (targetLength >= pathLength - lastSegmentLength) {
    const progress = (targetLength - (pathLength - lastSegmentLength)) / lastSegmentLength;
    return {
      x: points[points.length - 1].x + (points[0].x - points[points.length - 1].x) * progress,
      y: points[points.length - 1].y + (points[0].y - points[points.length - 1].y) * progress
    };
  }

  // 检查其他段
  for (let i = 0; i < points.length - 1; i++) {
    const segmentLength = Math.sqrt(
      Math.pow(points[i + 1].x - points[i].x, 2) +
      Math.pow(points[i + 1].y - points[i].y, 2)
    );

    if (targetLength <= accumulatedLength + segmentLength) {
      const progress = (targetLength - accumulatedLength) / segmentLength;
      return {
        x: points[i].x + (points[i + 1].x - points[i].x) * progress,
        y: points[i].y + (points[i + 1].y - points[i].y) * progress
      };
    }
    accumulatedLength += segmentLength;
  }

  return points[0]; // 默认返回起点
}

// 创建轮廓光点动画
const createOutlineAnimation = (points, duration = 3000) => {
  const animationId = Date.now() + Math.random();
  const pathLength = calculatePathLength(points);
  let startTime = null;

  // 创建光点元素数据
  const lightPoint = {
    id: animationId,
    x: points[0].x,
    y: points[0].y,
    visible: true
  };

  loadingAnimations.value.push(lightPoint);

  const animate = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const elapsed = timestamp - startTime;

    // 计算归一化的进度(0-1)
    const progress = (elapsed % duration) / duration;

    // 计算当前路径长度位置
    const currentLength = progress * pathLength;

    // 获取对应坐标点
    const position = getPointAtLength(points, pathLength, currentLength);

    // 更新光点位置
    const pointIndex = loadingAnimations.value.findIndex(p => p.id === animationId);
    if (pointIndex !== -1) {
      loadingAnimations.value[pointIndex].x = position.x;
      loadingAnimations.value[pointIndex].y = position.y;
    }

    // 继续动画
    const frameId = requestAnimationFrame(animate);
    animationFrames.value.push(frameId);
  };

  const frameId = requestAnimationFrame(animate);
  animationFrames.value.push(frameId);
}

const createLoadingAnimation = (points) => {
  // 使用新的轮廓动画替代原来的中心点动画
  createOutlineAnimation(points, 2500);
}

const stopAllLoadingAnimations = () => {
  // 停止所有动画帧
  animationFrames.value.forEach(frameId => {
    cancelAnimationFrame(frameId);
  });
  animationFrames.value = [];

  // 隐藏所有光点
  loadingAnimations.value.forEach(animation => {
    animation.visible = false;
  });

  // 0.5秒后清空数组
  setTimeout(() => {
    loadingAnimations.value = [];
  }, 500);
}

const drawEyeOutline = (ctx, points, color = '#00ffaa') => {
  // 绘制虚线轮廓
  ctx.beginPath();
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([8, 6]); // 虚线样式：8px实线，6px空白

  // 移动到第一个点
  ctx.moveTo(points[0].x, points[0].y);

  // 连接所有点
  for (let i = 1; i < points.length; i++) {
    ctx.lineTo(points[i].x, points[i].y);
  }

  // 闭合路径
  ctx.closePath();
  ctx.stroke();

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制关键点标记 - 使用更美观的样式
  points.forEach((point) => {
    // 绘制外圈发光效果
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 绘制中圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 绘制内圈实心点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 绘制中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制下巴角度的函数
const drawChinAngle = (ctx, jawPoints, chinAngle, color = '#ff6b6b', useTransformedPoints = false) => {
  console.log('开始绘制下巴角度，chinAngle:', chinAngle, 'useTransformedPoints:', useTransformedPoints);

  if (!jawPoints || jawPoints.length < 17) {
    console.log('jaw points 数据不足，无法绘制角度，当前长度:', jawPoints?.length);
    return;
  }

  // 根据landmarks数据，第5个点(id=5)、第8个点(id=8)、第12个点(id=12)
  let point5, point8, point11;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），直接使用索引
    point5 = jawPoints[5];
    point8 = jawPoints[8];
    point11 = jawPoints[11];
    console.log('使用转换后的点:', { point5, point8, point11 });
  } else {
    // 使用原始点，通过id查找
    point5 = jawPoints.find(p => p.id === 5); // 左下颌点
    point8 = jawPoints.find(p => p.id === 8); // 下巴尖（角度顶点）
    point11 = jawPoints.find(p => p.id === 12); // 右下颌点
    console.log('使用原始点:', { point5, point8, point11 });
  }

  if (!point5 || !point8 || !point11) {
    console.log('无法找到指定的jaw points');
    console.log('jawPoints:', jawPoints);
    return;
  }

  // 绘制两条线：point5 到 point8，point11 到 point8
  // 绘制外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 5;
  ctx.setLineDash([8, 6]);
  ctx.globalAlpha = 0.2;

  // 绘制第一条线的发光效果
  ctx.beginPath();
  ctx.moveTo(point5.x, point5.y);
  ctx.lineTo(point8.x, point8.y);
  ctx.stroke();

  // 绘制第二条线的发光效果
  ctx.beginPath();
  ctx.moveTo(point11.x, point11.y);
  ctx.lineTo(point8.x, point8.y);
  ctx.stroke();

  // 绘制主线
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.setLineDash([6, 4]);
  ctx.globalAlpha = 1;

  // 绘制第一条线 (point5 到 point8)
  ctx.beginPath();
  ctx.moveTo(point5.x, point5.y);
  ctx.lineTo(point8.x, point8.y);
  ctx.stroke();

  // 绘制第二条线 (point11 到 point8)
  ctx.beginPath();
  ctx.moveTo(point11.x, point11.y);
  ctx.lineTo(point8.x, point8.y);
  ctx.stroke();

  // 重置线条样式
  ctx.setLineDash([]);

  // 计算角度弧线的参数
  const radius = 40; // 角度弧线半径

  // 计算两条线的角度
  const angle1 = Math.atan2(point5.y - point8.y, point5.x - point8.x);
  const angle2 = Math.atan2(point11.y - point8.y, point11.x - point8.x);

  // 确保角度顺序正确
  let startAngle = Math.min(angle1, angle2);
  let endAngle = Math.max(angle1, angle2);

  // 如果角度差超过π，需要调整
  if (endAngle - startAngle > Math.PI) {
    [startAngle, endAngle] = [endAngle, startAngle + 2 * Math.PI];
  }

  // 绘制角度弧线 - 多层效果
  // 外层发光弧线
  ctx.beginPath();
  ctx.arc(point8.x, point8.y, radius + 3, startAngle, endAngle);
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.globalAlpha = 0.2;
  ctx.stroke();

  // 中层弧线
  ctx.beginPath();
  ctx.arc(point8.x, point8.y, radius + 1, startAngle, endAngle);
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.globalAlpha = 0.4;
  ctx.stroke();

  // 主弧线
  ctx.beginPath();
  ctx.arc(point8.x, point8.y, radius, startAngle, endAngle);
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.globalAlpha = 1;
  ctx.stroke();

  // 绘制角度标注
  const midAngle = (startAngle + endAngle) / 2;
  const textX = point8.x + Math.cos(midAngle) * (radius + 25);
  const textY = point8.y + Math.sin(midAngle) * (radius + 25);

  // 绘制角度文字背景 - 更美观的圆角背景
  const text = `${chinAngle.toFixed(1)}°`;
  ctx.font = 'bold 14px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 16;
  const bgHeight = 24;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(255, 107, 107, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制角度文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 4);

  // 绘制关键点标记 - 更美观的多层点
  [point5, point8, point11].forEach((point) => {
    // 最外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 10, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.15;
    ctx.stroke();

    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 7, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 5, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 3.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1.5, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.9;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制面部宽度的函数
const drawFaceWeight = (ctx, jawPoints, faceWeight, color = '#00ff88', useTransformedPoints = false) => {
  console.log('🎯 开始绘制面部宽度，faceWeight:', faceWeight, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 jawPoints数量:', jawPoints?.length);

  if (!jawPoints || jawPoints.length < 17) {
    console.log('❌ jaw points 数据不足，无法绘制面部宽度，当前长度:', jawPoints?.length);
    return;
  }

  // 根据landmarks数据，第0个点(id=0)和第16个点(id=16)
  let point0, point16;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），直接使用索引
    point0 = jawPoints[0];
    point16 = jawPoints[16];
    console.log('使用转换后的点:', { point0, point16 });
  } else {
    // 使用原始点，通过id查找
    point0 = jawPoints.find(p => p.id === 0); // 下颌左端点
    point16 = jawPoints.find(p => p.id === 16); // 下颌右端点
    console.log('使用原始点:', { point0, point16 });
  }

  if (!point0 || !point16) {
    console.log('❌ 无法找到指定的jaw points (0和16)');
    console.log('jawPoints:', jawPoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 0:', point0);
  console.log('  Point 16:', point16);

  // 计算连接线的中点
  const midX = (point0.x + point16.x) / 2;
  const midY = (point0.y + point16.y) / 2;

  // 计算连接线的角度
  const angle = Math.atan2(point16.y - point0.y, point16.x - point0.x);

  // 计算平行线的偏移距离（向上偏移）
  const offsetDistance = 40;
  const perpAngle = angle + Math.PI / 2; // 垂直方向

  // 计算平行线的中点位置（向上偏移，在屏幕坐标系中Y轴向下为正，所以向上是负偏移）
  const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
  const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;

  // 平行线的长度（比原连接线稍短）
  const parallelLength = Math.sqrt(Math.pow(point16.x - point0.x, 2) + Math.pow(point16.y - point0.y, 2)) * 0.8;

  // 计算平行线的起点和终点
  const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
  const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
  const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
  const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

  console.log('📏 平行线计算结果:');
  console.log('  连接线中点:', { x: midX, y: midY });
  console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
  console.log('  平行线起点:', { x: startX, y: startY });
  console.log('  平行线终点:', { x: endX, y: endY });
  console.log('  平行线长度:', parallelLength);
  console.log('  偏移距离:', offsetDistance);

  // 绘制连接线（point0到point16）
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.setLineDash([8, 6]);
  ctx.globalAlpha = 0.8;

  ctx.beginPath();
  ctx.moveTo(point0.x, point0.y);
  ctx.lineTo(point16.x, point16.y);
  ctx.stroke();

  console.log('🎨 开始绘制平行线...');

  // 绘制平行线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主平行线
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 平行线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 12;

  // 左箭头（尖朝左）
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 右箭头（尖朝右）
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在平行线中间）
  const textX = parallelMidX;
  const textY = parallelMidY;

  // 绘制距离文字背景
  const text = `${faceWeight}px`;
  ctx.font = 'bold 14px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 16;
  const bgHeight = 24;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(0, 255, 136, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 4);

  // 绘制关键点标记
  [point0, point16].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 8, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 5, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制左眼高度的函数
const drawLeftEyeHeight = (ctx, leftEyePoints, eyeHeight, color = '#ff6b9d', useTransformedPoints = false) => {
  console.log('🎯 开始绘制左眼高度，eyeHeight:', eyeHeight, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 leftEyePoints数量:', leftEyePoints?.length);

  if (!leftEyePoints || leftEyePoints.length < 6) {
    console.log('❌ left_eye points 数据不足，无法绘制左眼高度，当前长度:', leftEyePoints?.length);
    return;
  }

  // 根据landmarks数据，第44个点(id=44)和第46个点(id=46)
  let point44, point46;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），通过索引查找
    // left_eye的landmarks顺序：42, 43, 44, 45, 46, 47 对应索引 0, 1, 2, 3, 4, 5
    // id=44对应索引2，id=46对应索引4
    point44 = leftEyePoints.find(p => p.id === 44) || leftEyePoints[2]; // fallback到第2个点
    point46 = leftEyePoints.find(p => p.id === 46) || leftEyePoints[4]; // fallback到第4个点
    console.log('使用转换后的点（通过查找）:', { point44, point46 });
  } else {
    // 使用原始点，通过id查找
    point44 = leftEyePoints.find(p => p.id === 44);
    point46 = leftEyePoints.find(p => p.id === 46);
    console.log('使用原始点:', { point44, point46 });
  }

  // 如果没找到指定的点，使用left_eye的第2和第4个点作为替代
  if (!point44 || !point46) {
    console.log('⚠️ 未找到id=44和46的点，使用left_eye的替代点');
    point44 = leftEyePoints[2] || leftEyePoints[1]; // 左眼的第3个点或第2个点
    point46 = leftEyePoints[4] || leftEyePoints[leftEyePoints.length - 1]; // 左眼的第5个点或最后一个点
    console.log('使用替代点:', { point44, point46 });
  }

  if (!point44 || !point46) {
    console.log('❌ 无法找到合适的left_eye points');
    console.log('leftEyePoints:', leftEyePoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 44 (或替代):', point44);
  console.log('  Point 46 (或替代):', point46);

  // 计算连接线的中点
  const midX = (point44.x + point46.x) / 2;
  const midY = (point44.y + point46.y) / 2;

  // 计算连接线的角度
  const angle = Math.atan2(point46.y - point44.y, point46.x - point44.x);

  // 计算平行线的偏移距离（向外偏移，远离眼部轮廓）
  const offsetDistance = 30;
  const perpAngle = angle + Math.PI / 2; // 垂直方向

  // 计算平行线的中点位置（向外偏移）
  const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
  const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;

  // 平行线的长度（比原连接线稍短）
  const parallelLength = Math.sqrt(Math.pow(point46.x - point44.x, 2) + Math.pow(point46.y - point44.y, 2)) * 0.8;

  // 计算平行线的起点和终点
  const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
  const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
  const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
  const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

  console.log('📏 左眼高度平行线计算结果:');
  console.log('  连接线中点:', { x: midX, y: midY });
  console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
  console.log('  平行线起点:', { x: startX, y: startY });
  console.log('  平行线终点:', { x: endX, y: endY });
  console.log('  平行线长度:', parallelLength);
  console.log('  偏移距离:', offsetDistance);

  // 绘制连接线（point44到point46）
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([6, 4]);
  ctx.globalAlpha = 0.6;

  ctx.beginPath();
  ctx.moveTo(point44.x, point44.y);
  ctx.lineTo(point46.x, point46.y);
  ctx.stroke();

  console.log('🎨 开始绘制左眼高度平行线...');

  // 绘制平行线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主平行线
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 左眼高度平行线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 10;

  // 左箭头（尖朝左）
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 右箭头（尖朝右）
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在平行线中间）
  const textX = parallelMidX;
  const textY = parallelMidY;

  // 绘制距离文字背景
  const text = `${eyeHeight}px`;
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 12;
  const bgHeight = 20;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(255, 107, 157, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 3);

  // 绘制关键点标记
  [point44, point46].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制左眼宽度的函数
const drawLeftEyeWidth = (ctx, leftEyePoints, eyeWidth, color = '#9d6bff', useTransformedPoints = false) => {
  console.log('🎯 开始绘制左眼宽度，eyeWidth:', eyeWidth, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 leftEyePoints数量:', leftEyePoints?.length);

  if (!leftEyePoints || leftEyePoints.length < 6) {
    console.log('❌ left_eye points 数据不足，无法绘制左眼宽度，当前长度:', leftEyePoints?.length);
    return;
  }

  // 根据landmarks数据，第42个点(id=42)和第45个点(id=45)
  let point42, point45;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），通过索引查找
    // left_eye的landmarks顺序：42, 43, 44, 45, 46, 47 对应索引 0, 1, 2, 3, 4, 5
    // id=42对应索引0，id=45对应索引3
    point42 = leftEyePoints.find(p => p.id === 42) || leftEyePoints[0]; // fallback到第0个点
    point45 = leftEyePoints.find(p => p.id === 45) || leftEyePoints[3]; // fallback到第3个点
    console.log('使用转换后的点（通过查找）:', { point42, point45 });
  } else {
    // 使用原始点，通过id查找
    point42 = leftEyePoints.find(p => p.id === 42);
    point45 = leftEyePoints.find(p => p.id === 45);
    console.log('使用原始点:', { point42, point45 });
  }

  // 如果没找到指定的点，使用left_eye的第0和第3个点作为替代
  if (!point42 || !point45) {
    console.log('⚠️ 未找到id=42和45的点，使用left_eye的替代点');
    point42 = leftEyePoints[0] || leftEyePoints[0]; // 左眼的第1个点
    point45 = leftEyePoints[3] || leftEyePoints[leftEyePoints.length - 3]; // 左眼的第4个点
    console.log('使用替代点:', { point42, point45 });
  }

  if (!point42 || !point45) {
    console.log('❌ 无法找到合适的left_eye points');
    console.log('leftEyePoints:', leftEyePoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 42 (或替代):', point42);
  console.log('  Point 45 (或替代):', point45);

  // 计算连接线的中点
  const midX = (point42.x + point45.x) / 2;
  const midY = (point42.y + point45.y) / 2;

  // 计算连接线的角度
  const angle = Math.atan2(point45.y - point42.y, point45.x - point42.x);

  // 计算平行线的偏移距离（向外偏移，远离眼部轮廓）
  const offsetDistance = 35;
  const perpAngle = angle + Math.PI / 2; // 垂直方向

  // 计算平行线的中点位置（向外偏移，向下偏移）
  const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
  const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;

  // 平行线的长度（比原连接线稍长）
  const parallelLength = Math.sqrt(Math.pow(point45.x - point42.x, 2) + Math.pow(point45.y - point42.y, 2)) * 1.1;

  // 计算平行线的起点和终点
  const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
  const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
  const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
  const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

  console.log('📏 左眼宽度平行线计算结果:');
  console.log('  连接线中点:', { x: midX, y: midY });
  console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
  console.log('  平行线起点:', { x: startX, y: startY });
  console.log('  平行线终点:', { x: endX, y: endY });
  console.log('  平行线长度:', parallelLength);
  console.log('  偏移距离:', offsetDistance);

  // 绘制连接线（point42到point45）
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([6, 4]);
  ctx.globalAlpha = 0.6;

  ctx.beginPath();
  ctx.moveTo(point42.x, point42.y);
  ctx.lineTo(point45.x, point45.y);
  ctx.stroke();

  console.log('🎨 开始绘制左眼宽度平行线...');

  // 绘制平行线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主平行线
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 左眼宽度平行线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 10;

  // 左箭头（尖朝左）
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 右箭头（尖朝右）
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在平行线中间）
  const textX = parallelMidX;
  const textY = parallelMidY;

  // 绘制距离文字背景
  const text = `${eyeWidth}px`;
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 12;
  const bgHeight = 20;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(157, 107, 255, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 3);

  // 绘制关键点标记
  [point42, point45].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制右眼高度的函数
const drawRightEyeHeight = (ctx, rightEyePoints, eyeHeight, color = '#ff9d6b', useTransformedPoints = false) => {
  console.log('🎯 开始绘制右眼高度，eyeHeight:', eyeHeight, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 rightEyePoints数量:', rightEyePoints?.length);

  if (!rightEyePoints || rightEyePoints.length < 6) {
    console.log('❌ right_eye points 数据不足，无法绘制右眼高度，当前长度:', rightEyePoints?.length);
    return;
  }

  // 根据landmarks数据，第38个点(id=38)和第40个点(id=40)
  let point38, point40;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），通过索引查找
    // right_eye的landmarks顺序：36, 37, 38, 39, 40, 41 对应索引 0, 1, 2, 3, 4, 5
    // id=38对应索引2，id=40对应索引4
    point38 = rightEyePoints.find(p => p.id === 38) || rightEyePoints[2]; // fallback到第2个点
    point40 = rightEyePoints.find(p => p.id === 40) || rightEyePoints[4]; // fallback到第4个点
    console.log('使用转换后的点（通过查找）:', { point38, point40 });
  } else {
    // 使用原始点，通过id查找
    point38 = rightEyePoints.find(p => p.id === 38);
    point40 = rightEyePoints.find(p => p.id === 40);
    console.log('使用原始点:', { point38, point40 });
  }

  // 如果没找到指定的点，使用right_eye的第2和第4个点作为替代
  if (!point38 || !point40) {
    console.log('⚠️ 未找到id=38和40的点，使用right_eye的替代点');
    point38 = rightEyePoints[2] || rightEyePoints[1]; // 右眼的第3个点或第2个点
    point40 = rightEyePoints[4] || rightEyePoints[rightEyePoints.length - 1]; // 右眼的第5个点或最后一个点
    console.log('使用替代点:', { point38, point40 });
  }

  if (!point38 || !point40) {
    console.log('❌ 无法找到合适的right_eye points');
    console.log('rightEyePoints:', rightEyePoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 38 (或替代):', point38);
  console.log('  Point 40 (或替代):', point40);

  // 计算连接线的中点
  const midX = (point38.x + point40.x) / 2;
  const midY = (point38.y + point40.y) / 2;

  // 计算连接线的角度
  const angle = Math.atan2(point40.y - point38.y, point40.x - point38.x);

  // 计算平行线的偏移距离（向外偏移，远离眼部轮廓）
  const offsetDistance = 30;
  const perpAngle = angle + Math.PI / 2; // 垂直方向

  // 计算平行线的中点位置（向外偏移）
  const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
  const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;

  // 平行线的长度（比原连接线稍短）
  const parallelLength = Math.sqrt(Math.pow(point40.x - point38.x, 2) + Math.pow(point40.y - point38.y, 2)) * 0.8;

  // 计算平行线的起点和终点
  const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
  const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
  const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
  const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

  console.log('📏 右眼高度平行线计算结果:');
  console.log('  连接线中点:', { x: midX, y: midY });
  console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
  console.log('  平行线起点:', { x: startX, y: startY });
  console.log('  平行线终点:', { x: endX, y: endY });
  console.log('  平行线长度:', parallelLength);
  console.log('  偏移距离:', offsetDistance);

  // 绘制连接线（point38到point40）
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([6, 4]);
  ctx.globalAlpha = 0.6;

  ctx.beginPath();
  ctx.moveTo(point38.x, point38.y);
  ctx.lineTo(point40.x, point40.y);
  ctx.stroke();

  console.log('🎨 开始绘制右眼高度平行线...');

  // 绘制平行线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主平行线
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 右眼高度平行线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 10;

  // 左箭头（尖朝左）
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 右箭头（尖朝右）
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在平行线中间）
  const textX = parallelMidX;
  const textY = parallelMidY;

  // 绘制距离文字背景
  const text = `${eyeHeight}px`;
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 12;
  const bgHeight = 20;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(255, 157, 107, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 3);

  // 绘制关键点标记
  [point38, point40].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制右眼宽度的函数
const drawRightEyeWidth = (ctx, rightEyePoints, eyeWidth, color = '#6bff9d', useTransformedPoints = false) => {
  console.log('🎯 开始绘制右眼宽度，eyeWidth:', eyeWidth, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 rightEyePoints数量:', rightEyePoints?.length);

  if (!rightEyePoints || rightEyePoints.length < 6) {
    console.log('❌ right_eye points 数据不足，无法绘制右眼宽度，当前长度:', rightEyePoints?.length);
    return;
  }

  // 根据landmarks数据，第36个点(id=36)和第39个点(id=39)
  let point36, point39;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），通过索引查找
    // right_eye的landmarks顺序：36, 37, 38, 39, 40, 41 对应索引 0, 1, 2, 3, 4, 5
    // id=36对应索引0，id=39对应索引3
    point36 = rightEyePoints.find(p => p.id === 36) || rightEyePoints[0]; // fallback到第0个点
    point39 = rightEyePoints.find(p => p.id === 39) || rightEyePoints[3]; // fallback到第3个点
    console.log('使用转换后的点（通过查找）:', { point36, point39 });
  } else {
    // 使用原始点，通过id查找
    point36 = rightEyePoints.find(p => p.id === 36);
    point39 = rightEyePoints.find(p => p.id === 39);
    console.log('使用原始点:', { point36, point39 });
  }

  // 如果没找到指定的点，使用right_eye的第0和第3个点作为替代
  if (!point36 || !point39) {
    console.log('⚠️ 未找到id=36和39的点，使用right_eye的替代点');
    point36 = rightEyePoints[0] || rightEyePoints[0]; // 右眼的第1个点
    point39 = rightEyePoints[3] || rightEyePoints[rightEyePoints.length - 3]; // 右眼的第4个点
    console.log('使用替代点:', { point36, point39 });
  }

  if (!point36 || !point39) {
    console.log('❌ 无法找到合适的right_eye points');
    console.log('rightEyePoints:', rightEyePoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 36 (或替代):', point36);
  console.log('  Point 39 (或替代):', point39);

  // 计算连接线的中点
  const midX = (point36.x + point39.x) / 2;
  const midY = (point36.y + point39.y) / 2;

  // 计算连接线的角度
  const angle = Math.atan2(point39.y - point36.y, point39.x - point36.x);

  // 计算平行线的偏移距离（向外偏移，远离眼部轮廓）
  const offsetDistance = 35;
  const perpAngle = angle + Math.PI / 2; // 垂直方向

  // 计算平行线的中点位置（向外偏移，向下偏移）
  const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
  const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;

  // 平行线的长度（比原连接线稍长）
  const parallelLength = Math.sqrt(Math.pow(point39.x - point36.x, 2) + Math.pow(point39.y - point36.y, 2)) * 1.1;

  // 计算平行线的起点和终点
  const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
  const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
  const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
  const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

  console.log('📏 右眼宽度平行线计算结果:');
  console.log('  连接线中点:', { x: midX, y: midY });
  console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
  console.log('  平行线起点:', { x: startX, y: startY });
  console.log('  平行线终点:', { x: endX, y: endY });
  console.log('  平行线长度:', parallelLength);
  console.log('  偏移距离:', offsetDistance);

  // 绘制连接线（point36到point39）
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([6, 4]);
  ctx.globalAlpha = 0.6;

  ctx.beginPath();
  ctx.moveTo(point36.x, point36.y);
  ctx.lineTo(point39.x, point39.y);
  ctx.stroke();

  console.log('🎨 开始绘制右眼宽度平行线...');

  // 绘制平行线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主平行线
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 右眼宽度平行线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 10;

  // 左箭头（尖朝左）
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 右箭头（尖朝右）
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在平行线中间）
  const textX = parallelMidX;
  const textY = parallelMidY;

  // 绘制距离文字背景
  const text = `${eyeWidth}px`;
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 12;
  const bgHeight = 20;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(107, 255, 157, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 3);

  // 绘制关键点标记
  [point36, point39].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制左眉宽度的函数
const drawLeftEyebrowWidth = (ctx, leftEyebrowPoints, eyebrowWidth, color = '#ff6bff', useTransformedPoints = false) => {
  console.log('🎯 开始绘制左眉宽度，eyebrowWidth:', eyebrowWidth, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 leftEyebrowPoints数量:', leftEyebrowPoints?.length);

  if (!leftEyebrowPoints || leftEyebrowPoints.length < 5) {
    console.log('❌ left_eyebrow points 数据不足，无法绘制左眉宽度，当前长度:', leftEyebrowPoints?.length);
    return;
  }

  // 根据landmarks数据，第22个点(id=22)和第26个点(id=26)
  let point22, point26;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），通过索引查找
    // left_eyebrow的landmarks顺序：22, 23, 24, 25, 26 对应索引 0, 1, 2, 3, 4
    // id=22对应索引0，id=26对应索引4
    point22 = leftEyebrowPoints.find(p => p.id === 22) || leftEyebrowPoints[0]; // fallback到第0个点
    point26 = leftEyebrowPoints.find(p => p.id === 26) || leftEyebrowPoints[4]; // fallback到第4个点
    console.log('使用转换后的点（通过查找）:', { point22, point26 });
  } else {
    // 使用原始点，通过id查找
    point22 = leftEyebrowPoints.find(p => p.id === 22);
    point26 = leftEyebrowPoints.find(p => p.id === 26);
    console.log('使用原始点:', { point22, point26 });
  }

  // 如果没找到指定的点，使用left_eyebrow的第0和第4个点作为替代
  if (!point22 || !point26) {
    console.log('⚠️ 未找到id=22和26的点，使用left_eyebrow的替代点');
    point22 = leftEyebrowPoints[0] || leftEyebrowPoints[0]; // 左眉的第1个点
    point26 = leftEyebrowPoints[4] || leftEyebrowPoints[leftEyebrowPoints.length - 1]; // 左眉的第5个点
    console.log('使用替代点:', { point22, point26 });
  }

  if (!point22 || !point26) {
    console.log('❌ 无法找到合适的left_eyebrow points');
    console.log('leftEyebrowPoints:', leftEyebrowPoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 22 (或替代):', point22);
  console.log('  Point 26 (或替代):', point26);

  // 计算连接线的中点
  const midX = (point22.x + point26.x) / 2;
  const midY = (point22.y + point26.y) / 2;

  // 计算连接线的角度
  const angle = Math.atan2(point26.y - point22.y, point26.x - point22.x);

  // 计算平行线的偏移距离（向外偏移，远离眉毛轮廓）
  const offsetDistance = 25;
  const perpAngle = angle + Math.PI / 2; // 垂直方向

  // 计算平行线的中点位置（向外偏移，向上偏移）
  const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
  const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;

  // 平行线的长度（比原连接线稍长）
  const parallelLength = Math.sqrt(Math.pow(point26.x - point22.x, 2) + Math.pow(point26.y - point22.y, 2)) * 1.1;

  // 计算平行线的起点和终点
  const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
  const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
  const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
  const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

  console.log('📏 左眉宽度平行线计算结果:');
  console.log('  连接线中点:', { x: midX, y: midY });
  console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
  console.log('  平行线起点:', { x: startX, y: startY });
  console.log('  平行线终点:', { x: endX, y: endY });
  console.log('  平行线长度:', parallelLength);
  console.log('  偏移距离:', offsetDistance);

  // 绘制连接线（point22到point26）
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([6, 4]);
  ctx.globalAlpha = 0.6;

  ctx.beginPath();
  ctx.moveTo(point22.x, point22.y);
  ctx.lineTo(point26.x, point26.y);
  ctx.stroke();

  console.log('🎨 开始绘制左眉宽度平行线...');

  // 绘制平行线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主平行线
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 左眉宽度平行线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 10;

  // 左箭头（尖朝左）
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 右箭头（尖朝右）
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在平行线中间）
  const textX = parallelMidX;
  const textY = parallelMidY;

  // 绘制距离文字背景
  const text = `${eyebrowWidth}px`;
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 12;
  const bgHeight = 20;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(255, 107, 255, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 3);

  // 绘制关键点标记
  [point22, point26].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 绘制左眉高度的函数
const drawLeftEyebrowHeight = (ctx, leftEyebrowPoints, eyebrowHeight, color = '#6bffff', useTransformedPoints = false) => {
  console.log('🎯 开始绘制左眉高度，eyebrowHeight:', eyebrowHeight, 'useTransformedPoints:', useTransformedPoints);
  console.log('📊 leftEyebrowPoints数量:', leftEyebrowPoints?.length);

  if (!leftEyebrowPoints || leftEyebrowPoints.length < 5) {
    console.log('❌ left_eyebrow points 数据不足，无法绘制左眉高度，当前长度:', leftEyebrowPoints?.length);
    return;
  }

  // 根据landmarks数据，第22个点(id=22)、第24个点(id=24)和第26个点(id=26)
  let point22, point24, point26;

  if (useTransformedPoints) {
    // 如果使用转换后的点（放大模式），通过索引查找
    point22 = leftEyebrowPoints.find(p => p.id === 22) || leftEyebrowPoints[0];
    point24 = leftEyebrowPoints.find(p => p.id === 24) || leftEyebrowPoints[2];
    point26 = leftEyebrowPoints.find(p => p.id === 26) || leftEyebrowPoints[4];
    console.log('使用转换后的点（通过查找）:', { point22, point24, point26 });
  } else {
    // 使用原始点，通过id查找
    point22 = leftEyebrowPoints.find(p => p.id === 22);
    point24 = leftEyebrowPoints.find(p => p.id === 24);
    point26 = leftEyebrowPoints.find(p => p.id === 26);
    console.log('使用原始点:', { point22, point24, point26 });
  }

  // 如果没找到指定的点，使用替代点
  if (!point22 || !point24 || !point26) {
    console.log('⚠️ 未找到指定的点，使用left_eyebrow的替代点');
    point22 = point22 || leftEyebrowPoints[0];
    point24 = point24 || leftEyebrowPoints[2] || leftEyebrowPoints[Math.floor(leftEyebrowPoints.length / 2)];
    point26 = point26 || leftEyebrowPoints[4] || leftEyebrowPoints[leftEyebrowPoints.length - 1];
    console.log('使用替代点:', { point22, point24, point26 });
  }

  if (!point22 || !point24 || !point26) {
    console.log('❌ 无法找到合适的left_eyebrow points');
    console.log('leftEyebrowPoints:', leftEyebrowPoints);
    return;
  }

  console.log('✅ 找到关键点:');
  console.log('  Point 22 (或替代):', point22);
  console.log('  Point 24 (或替代):', point24);
  console.log('  Point 26 (或替代):', point26);

  // 计算22-26连接线的角度
  const baseAngle = Math.atan2(point26.y - point22.y, point26.x - point22.x);

  // 计算垂直线的角度（垂直于22-26连接线）
  const perpAngle = baseAngle + Math.PI / 2;

  // 计算垂直线的范围，不能超过第24个点和第26个点的垂直高度
  // 找到24和26点中Y坐标的最小值和最大值
  const minY = Math.min(point24.y, point26.y);
  const maxY = Math.max(point24.y, point26.y);

  // 计算垂直线的长度，限制在24-26点的Y范围内
  const yRange = maxY - minY;
  const verticalLength = yRange > 0 ? Math.min(yRange * 0.8, 25) : 20; // 限制最大长度

  // 从point24开始，绘制垂直线
  const halfLength = verticalLength / 2;
  let startX = point24.x - Math.cos(perpAngle) * halfLength;
  let startY = point24.y - Math.sin(perpAngle) * halfLength;
  let endX = point24.x + Math.cos(perpAngle) * halfLength;
  let endY = point24.y + Math.sin(perpAngle) * halfLength;

  // 确保垂直线的Y坐标不超出24-26点的范围
  if (startY < minY) {
    startY = minY;
    startX = point24.x - Math.cos(perpAngle) * Math.abs(startY - point24.y) / Math.abs(Math.sin(perpAngle));
  }
  if (startY > maxY) {
    startY = maxY;
    startX = point24.x - Math.cos(perpAngle) * Math.abs(startY - point24.y) / Math.abs(Math.sin(perpAngle));
  }
  if (endY < minY) {
    endY = minY;
    endX = point24.x + Math.cos(perpAngle) * Math.abs(endY - point24.y) / Math.abs(Math.sin(perpAngle));
  }
  if (endY > maxY) {
    endY = maxY;
    endX = point24.x + Math.cos(perpAngle) * Math.abs(endY - point24.y) / Math.abs(Math.sin(perpAngle));
  }

  console.log('📏 左眉高度垂直线计算结果:');
  console.log('  基准角度:', baseAngle * 180 / Math.PI, '度');
  console.log('  垂直角度:', perpAngle * 180 / Math.PI, '度');
  console.log('  Y范围限制:', { minY, maxY, yRange });
  console.log('  垂直线起点:', { x: startX, y: startY });
  console.log('  垂直线终点:', { x: endX, y: endY });
  console.log('  垂直线长度:', verticalLength);

  // 绘制22-26基准连接线（虚线）
  ctx.strokeStyle = color;
  ctx.lineWidth = 1;
  ctx.setLineDash([4, 3]);
  ctx.globalAlpha = 0.4;

  ctx.beginPath();
  ctx.moveTo(point22.x, point22.y);
  ctx.lineTo(point26.x, point26.y);
  ctx.stroke();

  console.log('🎨 开始绘制左眉高度垂直线...');

  // 绘制垂直线（增强可见性）
  // 外层发光效果
  ctx.strokeStyle = color;
  ctx.lineWidth = 4;
  ctx.setLineDash([]);
  ctx.globalAlpha = 0.3;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  // 主垂直线
  ctx.strokeStyle = color;
  ctx.lineWidth = 2.5;
  ctx.setLineDash([]);
  ctx.globalAlpha = 1;

  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.stroke();

  console.log('✅ 左眉高度垂直线绘制完成');

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制箭头（尖朝外）
  const arrowSize = 10;

  // 上箭头
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(perpAngle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(perpAngle - Math.PI + Math.PI/6));
  ctx.moveTo(startX, startY);
  ctx.lineTo(startX + arrowSize * Math.cos(perpAngle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(perpAngle - Math.PI - Math.PI/6));
  ctx.stroke();

  // 下箭头
  ctx.beginPath();
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(perpAngle + Math.PI/6), endY + arrowSize * Math.sin(perpAngle + Math.PI/6));
  ctx.moveTo(endX, endY);
  ctx.lineTo(endX + arrowSize * Math.cos(perpAngle - Math.PI/6), endY + arrowSize * Math.sin(perpAngle - Math.PI/6));
  ctx.stroke();

  // 绘制距离标注（在垂直线旁边）
  const textX = point24.x + Math.cos(baseAngle) * 20; // 沿着基准线方向偏移
  const textY = point24.y + Math.sin(baseAngle) * 20;

  // 绘制距离文字背景
  const text = `${eyebrowHeight}px`;
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'center';
  const textWidth = ctx.measureText(text).width;
  const bgWidth = textWidth + 12;
  const bgHeight = 20;

  // 背景阴影
  ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
  ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

  // 主背景
  ctx.fillStyle = 'rgba(107, 255, 255, 0.9)';
  ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 背景边框
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

  // 绘制距离文字
  ctx.fillStyle = '#fff';
  ctx.globalAlpha = 1;
  ctx.fillText(text, textX, textY + 3);

  // 绘制关键点标记
  [point22, point24, point26].forEach((point) => {
    // 外层发光圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.3;
    ctx.stroke();

    // 中层圈
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 1.5;
    ctx.globalAlpha = 0.6;
    ctx.stroke();

    // 主点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.globalAlpha = 1;
    ctx.fill();

    // 中心亮点
    ctx.beginPath();
    ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.8;
    ctx.fill();

    // 重置透明度
    ctx.globalAlpha = 1;
  });
}

// 缓动函数 - easeOutCubic
const easeOutCubic = (t) => {
  return 1 - Math.pow(1 - t, 3);
}

// 绘制点和连线的动画函数（支持局部放大）
const drawPointsAndLinesWithAnimation = (ctx, points, color, onComplete, zoomMode = true) => {
  const pointDuration = 200; // 每个点的绘制时间
  const lineDuration = 300;  // 每条线的绘制时间
  const pointDelay = 100;    // 点之间的延迟
  const lineDelay = 150;     // 线之间的延迟

  let drawnPoints = [];
  const canvasWidth = parseInt(canvasStyle.value.width.replace('px', ''));
  const canvasHeight = parseInt(canvasStyle.value.height.replace('px', ''));

  // 计算放大参数
  const boundingBox = zoomMode ? calculateBoundingBox(points) : null;
  const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

  // 转换点坐标到放大后的坐标系
  const transformedPoints = zoomMode && boundingBox && zoomParams
    ? points.map(point => transformPointToZoomed(point, boundingBox, zoomParams))
    : points;

  // 绘制单个点的动画
  const drawPointAnimation = (pointIndex) => {
    if (pointIndex >= transformedPoints.length) {
      // 所有点绘制完成，开始绘制连线
      setTimeout(() => drawLinesAnimation(), 200);
      return;
    }

    const point = transformedPoints[pointIndex];
    const startTime = Date.now();

    const animatePoint = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / pointDuration, 1);
      const easedProgress = easeOutCubic(progress);

      // 清除canvas（图片已经通过CSS变换同步放大）
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 重绘之前已完成的点（使用放大后的尺寸）
      drawnPoints.forEach(p => {
        const pointSize = zoomMode ? 3 : 2;
        const glowSize = zoomMode ? 5 : 4;

        ctx.beginPath();
        ctx.arc(p.x, p.y, pointSize, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();

        // 添加发光效果
        ctx.beginPath();
        ctx.arc(p.x, p.y, glowSize, 0, Math.PI * 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.3;
        ctx.stroke();
        ctx.globalAlpha = 1;
      });

      // 绘制当前动画中的点（放大模式下使用更大的尺寸）
      const baseRadius = zoomMode ? 4 : 3;
      const baseGlowRadius = zoomMode ? 8 : 6;

      const currentRadius = baseRadius * easedProgress;
      const glowRadius = baseGlowRadius * easedProgress;

      if (currentRadius > 0) {
        // 主点
        ctx.beginPath();
        ctx.arc(point.x, point.y, currentRadius, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();

        // 发光效果
        ctx.beginPath();
        ctx.arc(point.x, point.y, glowRadius, 0, Math.PI * 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.6 * easedProgress;
        ctx.stroke();
        ctx.globalAlpha = 1;

        // 额外的脉冲效果
        ctx.beginPath();
        ctx.arc(point.x, point.y, glowRadius * 1.5, 0, Math.PI * 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.2 * easedProgress;
        ctx.stroke();
        ctx.globalAlpha = 1;
      }

      ctx.draw(true);

      if (progress < 1) {
        requestAnimationFrame(animatePoint);
      } else {
        // 当前点绘制完成
        drawnPoints.push(point);
        setTimeout(() => drawPointAnimation(pointIndex + 1), pointDelay);
      }
    };

    requestAnimationFrame(animatePoint);
  };

  // 绘制连线的动画
  const drawLinesAnimation = () => {

    const drawLineAnimation = (lineIndex) => {
      if (lineIndex >= transformedPoints.length) {
        // 所有线绘制完成
        if (onComplete) onComplete();
        return;
      }

      const startPoint = transformedPoints[lineIndex];
      const endPoint = transformedPoints[(lineIndex + 1) % transformedPoints.length]; // 闭合连线
      const startTime = Date.now();

      const animateLine = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / lineDuration, 1);
        const easedProgress = easeOutCubic(progress);

        // 清除canvas（图片已经通过CSS变换同步放大）
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 重绘所有点（使用放大后的尺寸）
        drawnPoints.forEach(p => {
          const pointSize = zoomMode ? 3 : 2;
          const glowSize = zoomMode ? 5 : 4;

          ctx.beginPath();
          ctx.arc(p.x, p.y, pointSize, 0, Math.PI * 2);
          ctx.fillStyle = color;
          ctx.fill();

          // 点的发光效果
          ctx.beginPath();
          ctx.arc(p.x, p.y, glowSize, 0, Math.PI * 2);
          ctx.strokeStyle = color;
          ctx.lineWidth = 1;
          ctx.globalAlpha = 0.3;
          ctx.stroke();
          ctx.globalAlpha = 1;
        });

        // 重绘已完成的线（使用放大后的线宽）
        const lineWidth = zoomMode ? 2.5 : 2;
        const glowWidth = zoomMode ? 5 : 4;

        for (let i = 0; i < lineIndex; i++) {
          const start = transformedPoints[i];
          const end = transformedPoints[(i + 1) % transformedPoints.length];

          // 绘制外层发光效果
          ctx.beginPath();
          ctx.setLineDash([12, 8]); // 虚线样式
          ctx.moveTo(start.x, start.y);
          ctx.lineTo(end.x, end.y);
          ctx.strokeStyle = color;
          ctx.lineWidth = glowWidth;
          ctx.globalAlpha = 0.15;
          ctx.stroke();

          // 绘制中层发光
          ctx.beginPath();
          ctx.setLineDash([10, 6]);
          ctx.moveTo(start.x, start.y);
          ctx.lineTo(end.x, end.y);
          ctx.strokeStyle = color;
          ctx.lineWidth = lineWidth + 1;
          ctx.globalAlpha = 0.3;
          ctx.stroke();

          // 绘制主线
          ctx.beginPath();
          ctx.setLineDash([8, 6]);
          ctx.moveTo(start.x, start.y);
          ctx.lineTo(end.x, end.y);
          ctx.strokeStyle = color;
          ctx.lineWidth = lineWidth;
          ctx.globalAlpha = 1;
          ctx.stroke();

          // 重置线条样式
          ctx.setLineDash([]);
        }

        // 绘制当前动画中的线
        const currentX = startPoint.x + (endPoint.x - startPoint.x) * easedProgress;
        const currentY = startPoint.y + (endPoint.y - startPoint.y) * easedProgress;

        // 绘制外层发光效果
        ctx.beginPath();
        ctx.setLineDash([12, 8]);
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(currentX, currentY);
        ctx.strokeStyle = color;
        ctx.lineWidth = glowWidth;
        ctx.globalAlpha = 0.2;
        ctx.stroke();

        // 绘制中层发光
        ctx.beginPath();
        ctx.setLineDash([10, 6]);
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(currentX, currentY);
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth + 1;
        ctx.globalAlpha = 0.4;
        ctx.stroke();

        // 绘制主线
        ctx.beginPath();
        ctx.setLineDash([8, 6]);
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(currentX, currentY);
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.globalAlpha = 1;
        ctx.stroke();

        // 重置线条样式
        ctx.setLineDash([]);
        ctx.globalAlpha = 1;

        ctx.draw(true);

        if (progress < 1) {
          requestAnimationFrame(animateLine);
        } else {
          // 当前线绘制完成
          setTimeout(() => drawLineAnimation(lineIndex + 1), lineDelay);
        }
      };

      requestAnimationFrame(animateLine);
    };

    drawLineAnimation(0);
  };

  // 开始绘制点的动画
  drawPointAnimation(0);
}

// 计算部位的边界框
const calculateBoundingBox = (points) => {
  if (!points || points.length === 0) return null;

  let minX = points[0].x, maxX = points[0].x;
  let minY = points[0].y, maxY = points[0].y;

  points.forEach(point => {
    minX = Math.min(minX, point.x);
    maxX = Math.max(maxX, point.x);
    minY = Math.min(minY, point.y);
    maxY = Math.max(maxY, point.y);
  });

  // 添加一些边距
  const padding = 30;
  return {
    x: minX - padding,
    y: minY - padding,
    width: maxX - minX + padding * 2,
    height: maxY - minY + padding * 2,
    centerX: (minX + maxX) / 2,
    centerY: (minY + maxY) / 2,
    originalMinX: minX,
    originalMaxX: maxX,
    originalMinY: minY,
    originalMaxY: maxY
  };
};

// 计算放大参数
const calculateZoomParams = (boundingBox, canvasWidth, canvasHeight) => {
  // 目标放大区域大小（占canvas的60%）
  const targetWidth = canvasWidth * 0.6;
  const targetHeight = canvasHeight * 0.6;

  // 计算放大倍数
  const scaleX = targetWidth / boundingBox.width;
  const scaleY = targetHeight / boundingBox.height;
  const scale = Math.min(scaleX, scaleY, 3); // 最大放大3倍

  // 计算放大后的尺寸
  const zoomedWidth = boundingBox.width * scale;
  const zoomedHeight = boundingBox.height * scale;

  // 计算居中位置
  const zoomedX = (canvasWidth - zoomedWidth) / 2;
  const zoomedY = (canvasHeight - zoomedHeight) / 2;

  return {
    scale,
    zoomedX,
    zoomedY,
    zoomedWidth,
    zoomedHeight
  };
};

// 将原始坐标转换为放大后的坐标
const transformPointToZoomed = (point, boundingBox, zoomParams) => {
  // 计算点在原始边界框中的相对位置
  const relativeX = (point.x - boundingBox.x) / boundingBox.width;
  const relativeY = (point.y - boundingBox.y) / boundingBox.height;

  // 转换到放大后的坐标系
  return {
    x: zoomParams.zoomedX + relativeX * zoomParams.zoomedWidth,
    y: zoomParams.zoomedY + relativeY * zoomParams.zoomedHeight
  };
};

// 应用图片和canvas的同步放大效果
const applyZoomedEffect = (boundingBox, zoomParams, canvasWidth, canvasHeight) => {
  // 计算图片需要的变换参数
  const imageTransform = calculateImageTransform(boundingBox, zoomParams, canvasWidth, canvasHeight);

  // 应用图片变换
  imageStyle.value = {
    width: `${imageTransform.width}px`,
    height: `${imageTransform.height}px`,
    transform: `translate(${imageTransform.translateX}px, ${imageTransform.translateY}px) scale(${imageTransform.scale})`,
    transformOrigin: 'top left',
    transition: 'all 0.5s ease-in-out'
  };
};

// 计算图片变换参数
const calculateImageTransform = (boundingBox, zoomParams, canvasWidth, canvasHeight) => {
  // 计算原始图片在canvas中的位置和缩放
  const originalImageWidth = canvasWidth;
  const originalImageHeight = canvasHeight;

  // 计算需要放大的区域在原图中的相对位置
  const cropX = boundingBox.x / canvasWidth;
  const cropY = boundingBox.y / canvasHeight;

  // 计算图片的缩放和位移
  const scale = zoomParams.scale;
  const translateX = -cropX * originalImageWidth * scale + zoomParams.zoomedX;
  const translateY = -cropY * originalImageHeight * scale + zoomParams.zoomedY;

  return {
    width: originalImageWidth,
    height: originalImageHeight,
    scale: scale,
    translateX: translateX,
    translateY: translateY
  };
};

// 重置图片样式
const resetImageStyle = () => {
  imageStyle.value = {
    width: '100%',
    transform: 'none',
    transition: 'all 0.5s ease-in-out'
  };
};

// 创建局部放大的聚焦效果（仅绘制canvas装饰）
const createZoomedFocusEffect = (ctx, zoomParams) => {
  const x = zoomParams.zoomedX;
  const y = zoomParams.zoomedY;
  const w = zoomParams.zoomedWidth;
  const h = zoomParams.zoomedHeight;

  // 绘制外层发光边框
  ctx.strokeStyle = '#00E5FF';
  ctx.lineWidth = 4;
  ctx.globalAlpha = 0.3;
  ctx.setLineDash([15, 10]);
  ctx.strokeRect(x - 3, y - 3, w + 6, h + 6);

  // 绘制中层边框
  ctx.strokeStyle = '#40E0D0';
  ctx.lineWidth = 2.5;
  ctx.globalAlpha = 0.6;
  ctx.setLineDash([10, 8]);
  ctx.strokeRect(x - 1, y - 1, w + 2, h + 2);

  // 绘制主边框
  ctx.strokeStyle = '#00CED1';
  ctx.lineWidth = 2;
  ctx.globalAlpha = 1;
  ctx.setLineDash([8, 6]);
  ctx.strokeRect(x, y, w, h);

  // 重置线条样式
  ctx.setLineDash([]);

  // 绘制四个角的装饰 - 更美观的样式
  const cornerSize = 20;
  const cornerThickness = 3;

  // 外层角装饰
  ctx.strokeStyle = '#00E5FF';
  ctx.lineWidth = cornerThickness + 1;
  ctx.globalAlpha = 0.4;

  // 左上角
  ctx.beginPath();
  ctx.moveTo(x - 5, y + cornerSize);
  ctx.lineTo(x - 5, y - 5);
  ctx.lineTo(x + cornerSize, y - 5);
  ctx.stroke();

  // 右上角
  ctx.beginPath();
  ctx.moveTo(x + w - cornerSize, y - 5);
  ctx.lineTo(x + w + 5, y - 5);
  ctx.lineTo(x + w + 5, y + cornerSize);
  ctx.stroke();

  // 左下角
  ctx.beginPath();
  ctx.moveTo(x - 5, y + h - cornerSize);
  ctx.lineTo(x - 5, y + h + 5);
  ctx.lineTo(x + cornerSize, y + h + 5);
  ctx.stroke();

  // 右下角
  ctx.beginPath();
  ctx.moveTo(x + w - cornerSize, y + h + 5);
  ctx.lineTo(x + w + 5, y + h + 5);
  ctx.lineTo(x + w + 5, y + h - cornerSize);
  ctx.stroke();

  // 内层角装饰
  ctx.strokeStyle = '#40E0D0';
  ctx.lineWidth = cornerThickness;
  ctx.globalAlpha = 0.8;

  // 左上角
  ctx.beginPath();
  ctx.moveTo(x, y + cornerSize - 5);
  ctx.lineTo(x, y);
  ctx.lineTo(x + cornerSize - 5, y);
  ctx.stroke();

  // 右上角
  ctx.beginPath();
  ctx.moveTo(x + w - cornerSize + 5, y);
  ctx.lineTo(x + w, y);
  ctx.lineTo(x + w, y + cornerSize - 5);
  ctx.stroke();

  // 左下角
  ctx.beginPath();
  ctx.moveTo(x, y + h - cornerSize + 5);
  ctx.lineTo(x, y + h);
  ctx.lineTo(x + cornerSize - 5, y + h);
  ctx.stroke();

  // 右下角
  ctx.beginPath();
  ctx.moveTo(x + w - cornerSize + 5, y + h);
  ctx.lineTo(x + w, y + h);
  ctx.lineTo(x + w, y + h - cornerSize + 5);
  ctx.stroke();

  // 添加放大倍数标识 - 更美观的样式
  ctx.globalAlpha = 0.9;
  const scaleText = `${zoomParams.scale.toFixed(1)}x`;
  ctx.font = 'bold 14px Arial';
  ctx.textAlign = 'center';

  // 背景
  const textWidth = ctx.measureText(scaleText).width;
  ctx.fillStyle = 'rgba(0, 229, 255, 0.8)';
  ctx.fillRect(x + w - textWidth - 20, y + 5, textWidth + 16, 22);

  // 文字
  ctx.fillStyle = '#fff';
  ctx.fillText(scaleText, x + w - 12, y + 20);

  // 重置透明度
  ctx.globalAlpha = 1;
};

const initCanvasDrawing = (width, height, landmarks) => {
  // 设置canvas样式
  canvasStyle.value = {
    width: width + 'px',
    height: height + 'px'
  };

  showCanvas.value = true;

  // 等待canvas渲染完成后绘制
  nextTick(() => {
    setTimeout(() => {
      const ctx = uni.createCanvasContext('eyeCanvas');
      const canvasWidth = parseInt(width);
      const canvasHeight = parseInt(height);

      // 定义需要绘制的部位配置（按照landmarks对象的顺序）- 使用更美观的青色系配色
      const facialParts = [
        { name: 'jaw', color: '#00E5FF', label: '下颌轮廓' },        // 亮青色
        { name: 'right_eyebrow', color: '#40E0D0', label: '右眉毛' }, // 青绿色
        { name: 'left_eyebrow', color: '#48CAE4', label: '左眉毛' },  // 天蓝色
        { name: 'nose_bridge', color: '#0077BE', label: '鼻梁' },     // 深蓝色
        { name: 'nose_tip', color: '#00CED1', label: '鼻尖' },       // 深青色
        { name: 'right_eye', color: '#20B2AA', label: '右眼' },      // 浅海绿色
        { name: 'left_eye', color: '#5F9EA0', label: '左眼' },       // 军校蓝
        { name: 'outer_lips', color: '#4682B4', label: '外唇轮廓' }, // 钢蓝色
        { name: 'inner_lips', color: '#6495ED', label: '内唇轮廓' }  // 矢车菊蓝
      ];

      // 存储已绘制的部位
      let drawnParts = [];

      // 动画绘制每个部位
      let currentPartIndex = 0;

      const drawPartWithAnimation = () => {
        if (currentPartIndex >= facialParts.length) {
          // 所有部位绘制完成，重置图片和canvas，显示完整图像
          setTimeout(() => {
            // 重置图片样式
            resetImageStyle();

            // 清除canvas并重绘所有部位
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);
            drawnParts.forEach(part => {
              drawEyeOutline(ctx, part.points, part.color);

              // 如果是jaw轮廓，同时绘制下巴角度和面部宽度
              if (part.name === 'jaw') {
                console.log('🔍 处理jaw轮廓，检查数据:');
                console.log('  facialAngles.value:', facialAngles.value);
                console.log('  facialMeasurements.value:', facialMeasurements.value);

                if (facialAngles.value.chin_angle) {
                  drawChinAngle(ctx, part.points, facialAngles.value.chin_angle);
                }

                // 强制测试face_width绘制
                const testFaceWeight = facialMeasurements.value.face_width || 458; // 使用测试数据
                console.log('🧪 强制测试face_width绘制，使用数值:', testFaceWeight);
                drawFaceWeight(ctx, part.points, testFaceWeight);
              }

              // 如果是left_eye轮廓，绘制左眼高度和宽度
              if (part.name === 'left_eye') {
                console.log('🔍 处理left_eye轮廓，检查数据:');
                console.log('  facialMeasurements.value:', facialMeasurements.value);

                // 强制测试left_eye_height绘制
                const testLeftEyeHeight = facialMeasurements.value.left_eye_height || 40; // 使用测试数据
                console.log('🧪 强制测试left_eye_height绘制，使用数值:', testLeftEyeHeight);
                drawLeftEyeHeight(ctx, part.points, testLeftEyeHeight);

                // 强制测试left_eye_width绘制
                const testLeftEyeWidth = facialMeasurements.value.left_eye_width || 115; // 使用测试数据
                console.log('🧪 强制测试left_eye_width绘制，使用数值:', testLeftEyeWidth);
                drawLeftEyeWidth(ctx, part.points, testLeftEyeWidth);
              }

              // 如果是right_eye轮廓，绘制右眼高度和宽度
              if (part.name === 'right_eye') {
                console.log('🔍 处理right_eye轮廓，检查数据:');
                console.log('  facialMeasurements.value:', facialMeasurements.value);

                // 强制测试right_eye_height绘制
                const testRightEyeHeight = facialMeasurements.value.right_eye_height || 38; // 使用测试数据
                console.log('🧪 强制测试right_eye_height绘制，使用数值:', testRightEyeHeight);
                drawRightEyeHeight(ctx, part.points, testRightEyeHeight);

                // 强制测试right_eye_width绘制
                const testRightEyeWidth = facialMeasurements.value.right_eye_width || 115; // 使用测试数据
                console.log('🧪 强制测试right_eye_width绘制，使用数值:', testRightEyeWidth);
                drawRightEyeWidth(ctx, part.points, testRightEyeWidth);
              }

              // 如果是left_eyebrow轮廓，绘制左眉宽度和高度
              if (part.name === 'left_eyebrow') {
                console.log('🔍 处理left_eyebrow轮廓，检查数据:');
                console.log('  facialMeasurements.value:', facialMeasurements.value);

                // 强制测试left_eyebrow_width绘制
                const testLeftEyebrowWidth = facialMeasurements.value.left_eyebrow_width || 211; // 使用测试数据
                console.log('🧪 强制测试left_eyebrow_width绘制，使用数值:', testLeftEyebrowWidth);
                drawLeftEyebrowWidth(ctx, part.points, testLeftEyebrowWidth);

                // 强制测试left_eyebrow_height绘制
                const testLeftEyebrowHeight = facialMeasurements.value.left_eyebrow_height || 11; // 使用测试数据
                console.log('🧪 强制测试left_eyebrow_height绘制，使用数值:', testLeftEyebrowHeight);
                drawLeftEyebrowHeight(ctx, part.points, testLeftEyebrowHeight);
              }
            });
            ctx.draw();
          }, 500);
          return;
        }

        const currentPart = facialParts[currentPartIndex];
        const partData = landmarks[currentPart.name];

        if (!partData || partData.length === 0) {
          console.log(`${currentPart.name} 数据不存在或为空`);
          currentPartIndex++;
          setTimeout(drawPartWithAnimation, 500);
          return;
        }

        console.log(`开始绘制 ${currentPart.label}，包含 ${partData.length} 个点`);

        // 计算当前部位的边界框和放大参数
        const boundingBox = calculateBoundingBox(partData);
        const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

        if (boundingBox && zoomParams) {
          // 应用图片和canvas的同步放大效果
          applyZoomedEffect(boundingBox, zoomParams, canvasWidth, canvasHeight);

          // 清除画布
          ctx.clearRect(0, 0, canvasWidth, canvasHeight);

          // 创建放大聚焦效果（仅canvas装饰）
          createZoomedFocusEffect(ctx, zoomParams);

          // 绘制部位标签（在放大区域上方）- 更美观的样式
          const labelWidth = currentPart.label.length * 16 + 40;
          const labelHeight = 35;
          const labelX = zoomParams.zoomedX + (zoomParams.zoomedWidth - labelWidth) / 2;
          const labelY = zoomParams.zoomedY - 50;

          // 标签阴影
          ctx.globalAlpha = 0.3;
          ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
          ctx.fillRect(labelX + 3, labelY + 3, labelWidth, labelHeight);

          // 标签背景渐变效果
          ctx.globalAlpha = 0.95;
          const gradient = ctx.createLinearGradient(labelX, labelY, labelX, labelY + labelHeight);
          gradient.addColorStop(0, '#00E5FF');
          gradient.addColorStop(0.5, '#40E0D0');
          gradient.addColorStop(1, '#00CED1');
          ctx.fillStyle = gradient;
          ctx.fillRect(labelX, labelY, labelWidth, labelHeight);

          // 标签边框
          ctx.strokeStyle = '#fff';
          ctx.lineWidth = 2;
          ctx.globalAlpha = 0.8;
          ctx.strokeRect(labelX, labelY, labelWidth, labelHeight);

          // 标签文字
          ctx.globalAlpha = 1;
          ctx.fillStyle = '#fff';
          ctx.font = 'bold 18px Arial';
          ctx.textAlign = 'center';
          ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
          ctx.shadowBlur = 2;
          ctx.shadowOffsetX = 1;
          ctx.shadowOffsetY = 1;
          ctx.fillText(currentPart.label, zoomParams.zoomedX + zoomParams.zoomedWidth / 2, labelY + 23);

          // 重置阴影
          ctx.shadowColor = 'transparent';
          ctx.shadowBlur = 0;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;

          ctx.globalAlpha = 1;
          ctx.draw(true);
        }

        // 绘制当前部位的点和连线动画（使用放大模式）
        drawPointsAndLinesWithAnimation(ctx, partData, currentPart.color, () => {
          // 将当前部位添加到已绘制列表
          drawnParts.push({
            points: partData,
            color: currentPart.color,
            name: currentPart.name
          });

          // 如果是jaw轮廓，绘制完成后立即绘制下巴角度和面部宽度
          if (currentPart.name === 'jaw') {
            setTimeout(() => {
              // 在放大模式下，需要使用转换后的坐标
              const boundingBox = calculateBoundingBox(partData);
              const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

              if (boundingBox && zoomParams) {
                // 转换点坐标到放大后的坐标系
                const transformedPoints = partData.map(point => transformPointToZoomed(point, boundingBox, zoomParams));

                // 绘制下巴角度
                if (facialAngles.value.chin_angle) {
                  drawChinAngle(ctx, transformedPoints, facialAngles.value.chin_angle, '#ff6b6b', true);
                }

                // 绘制面部宽度
                const testFaceWeight = facialMeasurements.value.face_width || 458; // 使用测试数据
                console.log('🧪 放大模式强制测试face_width绘制，使用数值:', testFaceWeight);
                drawFaceWeight(ctx, transformedPoints, testFaceWeight, '#00ff88', true);
              } else {
                // 非放大模式
                if (facialAngles.value.chin_angle) {
                  drawChinAngle(ctx, partData, facialAngles.value.chin_angle);
                }
                const testFaceWeight = facialMeasurements.value.face_width || 458; // 使用测试数据
                console.log('🧪 正常模式强制测试face_width绘制，使用数值:', testFaceWeight);
                drawFaceWeight(ctx, partData, testFaceWeight);
              }
              ctx.draw(true);
            }, 300); // 延迟300ms后绘制角度和宽度
          }

          // 如果是left_eye轮廓，绘制完成后立即绘制左眼高度
          if (currentPart.name === 'left_eye') {
            setTimeout(() => {
              // 在放大模式下，需要使用转换后的坐标
              const boundingBox = calculateBoundingBox(partData);
              const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

              if (boundingBox && zoomParams) {
                // 转换点坐标到放大后的坐标系
                const transformedPoints = partData.map(point => transformPointToZoomed(point, boundingBox, zoomParams));

                // 绘制左眼高度
                const testLeftEyeHeight = facialMeasurements.value.left_eye_height || 40; // 使用测试数据
                console.log('🧪 放大模式强制测试left_eye_height绘制，使用数值:', testLeftEyeHeight);
                drawLeftEyeHeight(ctx, transformedPoints, testLeftEyeHeight, '#ff6b9d', true);

                // 绘制左眼宽度
                const testLeftEyeWidth = facialMeasurements.value.left_eye_width || 115; // 使用测试数据
                console.log('🧪 放大模式强制测试left_eye_width绘制，使用数值:', testLeftEyeWidth);
                drawLeftEyeWidth(ctx, transformedPoints, testLeftEyeWidth, '#9d6bff', true);
              } else {
                // 非放大模式
                const testLeftEyeHeight = facialMeasurements.value.left_eye_height || 40; // 使用测试数据
                console.log('🧪 正常模式强制测试left_eye_height绘制，使用数值:', testLeftEyeHeight);
                drawLeftEyeHeight(ctx, partData, testLeftEyeHeight);

                const testLeftEyeWidth = facialMeasurements.value.left_eye_width || 115; // 使用测试数据
                console.log('🧪 正常模式强制测试left_eye_width绘制，使用数值:', testLeftEyeWidth);
                drawLeftEyeWidth(ctx, partData, testLeftEyeWidth);
              }
              ctx.draw(true);
            }, 300); // 延迟300ms后绘制左眼高度
          }

          // 如果是right_eye轮廓，绘制完成后立即绘制右眼高度和宽度
          if (currentPart.name === 'right_eye') {
            setTimeout(() => {
              // 在放大模式下，需要使用转换后的坐标
              const boundingBox = calculateBoundingBox(partData);
              const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

              if (boundingBox && zoomParams) {
                // 转换点坐标到放大后的坐标系
                const transformedPoints = partData.map(point => transformPointToZoomed(point, boundingBox, zoomParams));

                // 绘制右眼高度
                const testRightEyeHeight = facialMeasurements.value.right_eye_height || 38; // 使用测试数据
                console.log('🧪 放大模式强制测试right_eye_height绘制，使用数值:', testRightEyeHeight);
                drawRightEyeHeight(ctx, transformedPoints, testRightEyeHeight, '#ff9d6b', true);

                // 绘制右眼宽度
                const testRightEyeWidth = facialMeasurements.value.right_eye_width || 115; // 使用测试数据
                console.log('🧪 放大模式强制测试right_eye_width绘制，使用数值:', testRightEyeWidth);
                drawRightEyeWidth(ctx, transformedPoints, testRightEyeWidth, '#6bff9d', true);
              } else {
                // 非放大模式
                const testRightEyeHeight = facialMeasurements.value.right_eye_height || 38; // 使用测试数据
                console.log('🧪 正常模式强制测试right_eye_height绘制，使用数值:', testRightEyeHeight);
                drawRightEyeHeight(ctx, partData, testRightEyeHeight);

                const testRightEyeWidth = facialMeasurements.value.right_eye_width || 115; // 使用测试数据
                console.log('🧪 正常模式强制测试right_eye_width绘制，使用数值:', testRightEyeWidth);
                drawRightEyeWidth(ctx, partData, testRightEyeWidth);
              }
              ctx.draw(true);
            }, 300); // 延迟300ms后绘制右眼高度和宽度
          }

          // 如果是left_eyebrow轮廓，绘制完成后立即绘制左眉宽度和高度
          if (currentPart.name === 'left_eyebrow') {
            setTimeout(() => {
              // 在放大模式下，需要使用转换后的坐标
              const boundingBox = calculateBoundingBox(partData);
              const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

              if (boundingBox && zoomParams) {
                // 转换点坐标到放大后的坐标系
                const transformedPoints = partData.map(point => transformPointToZoomed(point, boundingBox, zoomParams));

                // 绘制左眉宽度
                const testLeftEyebrowWidth = facialMeasurements.value.left_eyebrow_width || 211; // 使用测试数据
                console.log('🧪 放大模式强制测试left_eyebrow_width绘制，使用数值:', testLeftEyebrowWidth);
                drawLeftEyebrowWidth(ctx, transformedPoints, testLeftEyebrowWidth, '#ff6bff', true);

                // 绘制左眉高度
                const testLeftEyebrowHeight = facialMeasurements.value.left_eyebrow_height || 11; // 使用测试数据
                console.log('🧪 放大模式强制测试left_eyebrow_height绘制，使用数值:', testLeftEyebrowHeight);
                drawLeftEyebrowHeight(ctx, transformedPoints, testLeftEyebrowHeight, '#6bffff', true);
              } else {
                // 非放大模式
                const testLeftEyebrowWidth = facialMeasurements.value.left_eyebrow_width || 211; // 使用测试数据
                console.log('🧪 正常模式强制测试left_eyebrow_width绘制，使用数值:', testLeftEyebrowWidth);
                drawLeftEyebrowWidth(ctx, partData, testLeftEyebrowWidth);

                const testLeftEyebrowHeight = facialMeasurements.value.left_eyebrow_height || 11; // 使用测试数据
                console.log('🧪 正常模式强制测试left_eyebrow_height绘制，使用数值:', testLeftEyebrowHeight);
                drawLeftEyebrowHeight(ctx, partData, testLeftEyebrowHeight);
              }
              ctx.draw(true);
            }, 300); // 延迟300ms后绘制左眉宽度和高度
          }

          currentPartIndex++;
          setTimeout(drawPartWithAnimation, 1200); // 每个部位完成后延迟1.2秒绘制下一个
        }, true); // 启用放大模式
      };

      // 开始动画绘制
      drawPartWithAnimation();

    }, 100);
  });
}

const handlePreview = () => {
  // 防止iOS端快速点击导致的状态混乱
  if (isAnimating.value) return

  // 检查用户是否已登录
  const userInfo = uni.getStorageSync('userInfo')

  if (!userInfo) {
    // 未登录，通知父组件显示登录弹窗
    emit('show-login')
    return
  }

  // 已登录，执行原有逻辑
  expandPreview()
}

const expandPreview = () => {
  // if (beforeimgRef.value) {
  //   beforeImgHeight.value = beforeimgRef.value.height
  // }

  // 添加动画状态控制
  isAnimating.value = true
  isExpanded.value = !isExpanded.value

  // 动画完成后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}
let face_aes = ref({})
let detect = ref({})
let generate = ref({})
async function getStatus(operationId) {
  let { data } = await callAiPolling({ operationId, type: 'esthetics' })

  // 计算进度：aiStatus为'1'的数量 / 总数量 * 100
  const completedCount = data.data.filter(item => item.aiStatus == '1').length
  const totalCount = data.data.length
  const percent = Math.round((completedCount / totalCount) * 100)

  // 更新进度
  emit('update:percent', percent)

  // let status = data.data.every(item => item.aiStatus == '1')
  let generateStatus = data.data.filter(item => item.aiType == 'generate')[0].aiStatus
  let faceAesStatus = data.data.filter(item => item.aiType == 'face_aes')[0].aiStatus
  if (faceAesStatus == '1') {
    face_aes.value = JSON.parse(data.data.filter(item => item.aiType == 'face_aes')[0].aiResult)

  } else if (faceAesStatus == '2') {
    // uni.showModal({
    //   title: '提示',
    //   content: '生成图片失败',
    //   confirmText:"重新上传",
    //   success: function (res) {
    //     if (res.confirm) {
    //       uni.navigateBack();
    //     } else if (res.cancel) {
    //       console.log('用户点击取消');
    //     }
    //   }
    // });

    // uni.showToast({
    //   title: '生成图片失败',
    //   duration: 2000,
    //   icon:"none"
    // });
    // clearInterval(timer)
    // stopAllLoadingAnimations()
  }
  let status = data.data.every(item => item.aiStatus == '1')
  if (status) {

    clearInterval(timer)
  }
  if (generateStatus == '1') {
    generate.value = data.data.filter(item => item.aiType == 'generate')[0].aiResult
    // 完成后隐藏loading
    // emit('update:loading', false)
    // emit('update:percent', 100)

    // 10秒后停止加载动画
    stopAllLoadingAnimations()
  } else if (generateStatus == '2') {
    // uni.showModal({
    //   title: '提示',
    //   content: '生成图片失败',
    //   confirmText:"重新上传",
    //   success: function (res) {
    //     if (res.confirm) {
    //       uni.navigateBack();
    //     } else if (res.cancel) {
    //       console.log('用户点击取消');
    //     }
    //   }
    // });

    // uni.showToast({
    //   title: '生成图片失败',
    //   duration: 2000,
    //   icon:"none"
    // });
    // clearInterval(timer)
    // stopAllLoadingAnimations()
  }
}

function organizeData(data) {

  face_aes.value = JSON.parse(data.filter(item => item.aiType == 'face_aes')[0].aiResult)
  detect.value = JSON.parse(data.filter(item => item.aiType == 'detect')[0].aiResult)
  generate.value = data.filter(item => item.aiType == 'generate')[0].aiResult

}
const popupVisible = ref(false)
const selectIcon2 = (item) => {
  // activeReport.value = item;
  emit('update:activeReport', item)

  // 重置展开状态，确保每次切换tab时都是收起状态
  isExpanded.value = false
  isAnimating.value = false

  // iOS端需要更长的延迟来确保DOM更新完成
  setTimeout(() => {
    popupVisible.value = true;
  }, 100);
}

const gotoOrgList = () => {
  uni.navigateTo({
    url: '/pages/orgList/index'
  })
}


// 推荐项目数据
const recommendedItems = ref([
  {
    imgSrc: '/static/imgs/ject1.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "明星医师方案",
    category: "玻尿酸+肉毒素+光子嫩肤",
  },
  {
    imgSrc: '/static/imgs/ject2.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "黄金标准方案",
    category: "玻尿酸+肉毒素",
  },
  {
    imgSrc: '/static/imgs/ject3.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "经典臻选方案",
    category: "玻尿酸",
  },
]);



watch(() => props.activeReport, () => {
  // 监听activeReport变化，重置展开状态
  isExpanded.value = false
  isAnimating.value = false
}, { deep: true });

// 暴露方法给父组件调用
defineExpose({
  expandPreview
})
</script>

<style lang="scss" scoped>
/* 标签样式 */
.tag {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* 弹窗样式 */
.popup-overlay {

  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.popup-container {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 4;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  /* iOS兼容性：使用fallback背景色 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top-left-radius: 5rpx;
  border-top-right-radius: 5rpx;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 110rpx;
  margin-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  /* 兼容 IOS<11.2 */
  margin-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* 兼容 IOS>11.2 */
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS安全区域适配 */
  overflow: hidden;
  will-change: transform, height;
  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
  /* iOS点击优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  /* 强制硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  &>view {
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-content {
      flex: 1;
      overflow-y: auto;
      /* iOS滚动优化 */
      -webkit-overflow-scrolling: touch;
    }
  }
}

.popup-container.collapsed {
  height: 200rpx;
  overflow: hidden;
}

.collapsed {
  .popup-content {
    overflow: hidden !important;
  }
}

.popup-container.expanded {
  max-height: 60vh;
  height: auto;
  overflow-y: auto;
  min-height: 0;
}

.popup-container.slide-up {
  transform: translateY(0) translateZ(0);
  /* iOS优化：强制硬件加速 */
  -webkit-transform: translateY(0) translateZ(0);
}

.popup-container.slide-down {
  transform: translateY(100%) translateZ(0);
  /* iOS优化：强制硬件加速 */
  -webkit-transform: translateY(100%) translateZ(0);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.popup-content {
  width: 100%;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  transition: none;
  /* 移除内容区域的过渡动画 */
  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
}

.popup-container.expanded .popup-content {
  overflow-y: auto;
}

.popup-container.collapsed .popup-content {
  overflow-y: auto;
}

/* 横向滚动样式 */
scroll-view ::v-deep {
  .flex {
    flex-wrap: nowrap;
  }
}

/* 底部标签栏样式 */
.bottom-tab {
  position: relative;
}

.bottom-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 4rpx;
  background-color: #ff4d4f;
}

.inactive-tab {
  color: #999;
}

/* 新增iOS适配样式 */
.ios-safe-area {
  padding-top: env(safe-area-inset-top);
}

.ios-bottom-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS滚动优化 */
scroll-view {
  -webkit-overflow-scrolling: touch;
}

/* iOS按钮点击效果优化 */
button {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* iOS特定按钮样式 */
.ios-button {
  /* 防止iOS双击缩放 */
  touch-action: manipulation;
  /* 强制硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 防止点击延迟 */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  /* 防止长按选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

/* iOS图片优化 */
img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Canvas和光点动画样式 */
.image-container {
  position: relative;
  display: inline-block;
  overflow: hidden; /* 防止聚焦效果溢出 */
}

.light-point {
  position: absolute;
  width: 14px;
  height: 14px;
  background: radial-gradient(circle, #00E5FF 0%, #40E0D0 50%, #00CED1 100%);
  border-radius: 50%;
  box-shadow:
    0 0 10px #00E5FF,
    0 0 20px #40E0D0,
    0 0 30px #00CED1,
    inset 0 0 5px rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  z-index: 3;
  transition: all 0.05s linear;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

/* Canvas聚焦动画样式 */
#eyeCanvas {
  transition: all 0.3s ease-in-out;
  will-change: transform;
}

/* 聚焦时的脉冲动画 - 更美观的青色系 */
@keyframes focusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 229, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 229, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 229, 255, 0);
  }
}

/* 光点闪烁动画 */
@keyframes lightPointGlow {
  0%, 100% {
    box-shadow:
      0 0 10px #00E5FF,
      0 0 20px #40E0D0,
      0 0 30px #00CED1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    box-shadow:
      0 0 15px #00E5FF,
      0 0 25px #40E0D0,
      0 0 35px #00CED1,
      0 0 45px #48CAE4;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 轮廓线闪烁动画 */
@keyframes outlineGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px #00E5FF);
  }
  50% {
    filter: drop-shadow(0 0 10px #40E0D0) drop-shadow(0 0 15px #00CED1);
  }
}

.focus-active {
  animation: focusPulse 2s infinite;
}

.light-point {
  // animation: lightPointGlow 2s ease-in-out infinite;
}

#eyeCanvas {
  // animation: outlineGlow 3s ease-in-out infinite;
}
</style>