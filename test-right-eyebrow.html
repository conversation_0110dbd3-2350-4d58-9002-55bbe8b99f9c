<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右眉宽度和高度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>右眉宽度和高度绘制测试</h1>
        <p>测试在right_eyebrow轮廓外侧绘制平行线和垂直线显示right_eyebrow_width和right_eyebrow_height</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div class="info">
            <h3>测试说明</h3>
            <p><strong>蓝色虚线:</strong> right_eyebrow轮廓</p>
            <p><strong>黄色实线:</strong> right_eyebrow_width（第17和21点的平行线）</p>
            <p><strong>粉色实线:</strong> right_eyebrow_height（第19点的垂直线，不超过19-21点的垂直高度）</p>
            <p><strong>对应箭头:</strong> 各自线条两端的朝外箭头</p>
            <p><strong>对应文字:</strong> 各自的数值标注</p>
        </div>
    </div>

    <script>
        // 测试数据 - 模拟right_eyebrow的landmarks
        const testData = {
            rightEyebrowPoints: [
                { x: 150, y: 120, id: 17 }, // 右眉第1个点 - 宽度测量点1
                { x: 190, y: 110, id: 18 }, // 右眉第2个点
                { x: 230, y: 105, id: 19 }, // 右眉第3个点 - 高度测量点
                { x: 270, y: 110, id: 20 }, // 右眉第4个点
                { x: 310, y: 120, id: 21 }  // 右眉第5个点 - 宽度测量点2
            ],
            rightEyebrowWidth: 211,
            rightEyebrowHeight: 11
        };

        // 绘制右眉宽度的函数（简化版）
        function drawRightEyebrowWidth(ctx, rightEyebrowPoints, eyebrowWidth) {
            console.log('🎯 开始绘制右眉宽度测试');

            // 使用第17和第21个点（id=17和id=21）
            const point17 = rightEyebrowPoints.find(p => p.id === 17);
            const point21 = rightEyebrowPoints.find(p => p.id === 21);

            console.log('使用的点:', { point17, point21 });

            // 计算连接线的中点
            const midX = (point17.x + point21.x) / 2;
            const midY = (point17.y + point21.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point21.y - point17.y, point21.x - point17.x);
            
            // 计算平行线的偏移距离（向外偏移，向上偏移）
            const offsetDistance = 25;
            const perpAngle = angle + Math.PI / 2;
            
            // 计算平行线的中点位置（向外偏移，向上偏移）
            const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍长）
            const parallelLength = Math.sqrt(Math.pow(point21.x - point17.x, 2) + Math.pow(point21.y - point17.y, 2)) * 1.1;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point17.x, point17.y);
            ctx.lineTo(point21.x, point21.y);
            ctx.stroke();

            // 绘制平行线（黄色实线）
            ctx.strokeStyle = '#ffff6b';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（黄色）
            const arrowSize = 10;
            ctx.strokeStyle = '#ffff6b';
            ctx.lineWidth = 2;
            
            // 左箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（黄色背景）
            const text = `${eyebrowWidth}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(255, 255, 107, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point17, point21].forEach((point) => {
                ctx.fillStyle = '#ffff6b';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 右眉宽度绘制完成');
        }

        // 绘制右眉高度的函数（简化版）
        function drawRightEyebrowHeight(ctx, rightEyebrowPoints, eyebrowHeight) {
            console.log('🎯 开始绘制右眉高度测试');

            // 使用第17、19和21个点
            const point17 = rightEyebrowPoints.find(p => p.id === 17);
            const point19 = rightEyebrowPoints.find(p => p.id === 19);
            const point21 = rightEyebrowPoints.find(p => p.id === 21);

            console.log('使用的点:', { point17, point19, point21 });

            // 计算17-21连接线的角度
            const baseAngle = Math.atan2(point21.y - point17.y, point21.x - point17.x);
            
            // 计算垂直线的角度（垂直于17-21连接线）
            const perpAngle = baseAngle + Math.PI / 2;
            
            // 计算垂直线的范围，不能超过第19个点和第21个点的垂直高度
            const minY = Math.min(point19.y, point21.y);
            const maxY = Math.max(point19.y, point21.y);
            
            // 计算垂直线的长度，限制在19-21点的Y范围内
            const yRange = maxY - minY;
            const verticalLength = yRange > 0 ? Math.min(yRange * 0.8, 25) : 20;
            
            // 从point19开始，绘制垂直线
            const halfLength = verticalLength / 2;
            let startX = point19.x - Math.cos(perpAngle) * halfLength;
            let startY = point19.y - Math.sin(perpAngle) * halfLength;
            let endX = point19.x + Math.cos(perpAngle) * halfLength;
            let endY = point19.y + Math.sin(perpAngle) * halfLength;
            
            // 确保垂直线的Y坐标不超出19-21点的范围
            if (startY < minY) {
                startY = minY;
                startX = point19.x - Math.cos(perpAngle) * Math.abs(startY - point19.y) / Math.abs(Math.sin(perpAngle));
            }
            if (startY > maxY) {
                startY = maxY;
                startX = point19.x - Math.cos(perpAngle) * Math.abs(startY - point19.y) / Math.abs(Math.sin(perpAngle));
            }
            if (endY < minY) {
                endY = minY;
                endX = point19.x + Math.cos(perpAngle) * Math.abs(endY - point19.y) / Math.abs(Math.sin(perpAngle));
            }
            if (endY > maxY) {
                endY = maxY;
                endX = point19.x + Math.cos(perpAngle) * Math.abs(endY - point19.y) / Math.abs(Math.sin(perpAngle));
            }

            // 绘制17-21基准连接线（虚线）
            ctx.strokeStyle = '#ff6baa';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.4;
            
            ctx.beginPath();
            ctx.moveTo(point17.x, point17.y);
            ctx.lineTo(point21.x, point21.y);
            ctx.stroke();

            // 绘制垂直线（粉色实线）
            ctx.strokeStyle = '#ff6baa';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（粉色）
            const arrowSize = 10;
            ctx.strokeStyle = '#ff6baa';
            ctx.lineWidth = 2;
            
            // 上箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(perpAngle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(perpAngle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(perpAngle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(perpAngle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 下箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(perpAngle + Math.PI/6), endY + arrowSize * Math.sin(perpAngle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(perpAngle - Math.PI/6), endY + arrowSize * Math.sin(perpAngle - Math.PI/6));
            ctx.stroke();

            // 绘制距离标注（在垂直线旁边）
            const textX = point19.x + Math.cos(baseAngle) * 20;
            const textY = point19.y + Math.sin(baseAngle) * 20;

            // 绘制数值标注（粉色背景）
            const text = `${eyebrowHeight}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(255, 107, 170, 0.9)';
            ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, textX, textY + 3);

            // 绘制关键点
            [point17, point19, point21].forEach((point) => {
                ctx.fillStyle = '#ff6baa';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 右眉高度绘制完成');
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制right_eyebrow轮廓（蓝色虚线）
            ctx.strokeStyle = '#5F9EA0';
            ctx.lineWidth = 2;
            ctx.setLineDash([6, 4]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(testData.rightEyebrowPoints[0].x, testData.rightEyebrowPoints[0].y);
            for (let i = 1; i < testData.rightEyebrowPoints.length; i++) {
                ctx.lineTo(testData.rightEyebrowPoints[i].x, testData.rightEyebrowPoints[i].y);
            }
            ctx.stroke();

            // 绘制所有right_eyebrow点
            testData.rightEyebrowPoints.forEach((point, index) => {
                ctx.fillStyle = '#5F9EA0';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '8px Arial';
                ctx.fillText(`${point.id}`, point.x + 5, point.y - 5);
            });

            // 绘制右眉宽度
            drawRightEyebrowWidth(ctx, testData.rightEyebrowPoints, testData.rightEyebrowWidth);

            // 绘制右眉高度
            drawRightEyebrowHeight(ctx, testData.rightEyebrowPoints, testData.rightEyebrowHeight);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
