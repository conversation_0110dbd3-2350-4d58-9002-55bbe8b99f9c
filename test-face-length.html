<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面部长度渲染测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .data-section {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .highlight {
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面部宽度渲染测试</h1>
        <p>这个测试页面模拟了在jaw轮廓上渲染face_weight的功能</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="600"></canvas>
        </div>
        
        <div class="info">
            <h3>测试数据</h3>
            <div class="data-section">
                <strong>Face Weight:</strong> <span class="highlight">458px</span>
            </div>
            <div class="data-section">
                <strong>Jaw Points (0和16):</strong><br>
                Point 0: <span class="highlight">x: 391, y: 646</span><br>
                Point 16: <span class="highlight">x: 974, y: 667</span>
            </div>
            <div class="data-section">
                <strong>功能说明:</strong><br>
                • 连接jaw的第0个点和第16个点<br>
                • 在连接线的中点绘制垂直延长线<br>
                • 延长线两端添加箭头<br>
                • 在延长线旁边显示face_weight数值
            </div>
        </div>
    </div>

    <script>
        // 测试数据 - 来自用户提供的detectInfo
        const testData = {
            faceWeight: 458,
            jawPoints: [
                {id: 0, x: 391, y: 646},
                {id: 1, x: 394, y: 730},
                {id: 2, x: 407, y: 814},
                {id: 3, x: 425, y: 895},
                {id: 4, x: 456, y: 971},
                {id: 5, x: 503, y: 1038},
                {id: 6, x: 556, y: 1092},
                {id: 7, x: 615, y: 1137},
                {id: 8, x: 678, y: 1152},
                {id: 9, x: 742, y: 1137},
                {id: 10, x: 802, y: 1092},
                {id: 11, x: 857, y: 1038},
                {id: 12, x: 903, y: 977},
                {id: 13, x: 933, y: 905},
                {id: 14, x: 953, y: 829},
                {id: 15, x: 968, y: 749},
                {id: 16, x: 974, y: 667}
            ]
        };

        // 缩放因子，将原始坐标适配到canvas
        const scale = 0.5;
        const offsetX = 50;
        const offsetY = -250;

        // 绘制面部宽度的函数（从Vue组件移植过来）
        function drawFaceWeight(ctx, jawPoints, faceWeight, color = '#00ff88') {
            console.log('开始绘制面部宽度，faceWeight:', faceWeight);

            if (!jawPoints || jawPoints.length < 17) {
                console.log('jaw points 数据不足，无法绘制面部宽度');
                return;
            }

            // 获取第0个点和第16个点
            const point0 = jawPoints.find(p => p.id === 0);
            const point16 = jawPoints.find(p => p.id === 16);

            if (!point0 || !point16) {
                console.log('无法找到指定的jaw points (0和16)');
                return;
            }

            // 应用缩放和偏移
            const scaledPoint0 = {
                x: point0.x * scale + offsetX,
                y: point0.y * scale + offsetY
            };
            const scaledPoint16 = {
                x: point16.x * scale + offsetX,
                y: point16.y * scale + offsetY
            };

            // 计算连接线的中点
            const midX = (scaledPoint0.x + scaledPoint16.x) / 2;
            const midY = (scaledPoint0.y + scaledPoint16.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(scaledPoint16.y - scaledPoint0.y, scaledPoint16.x - scaledPoint0.x);
            
            // 计算垂直延长线的方向（垂直于连接线）
            const perpAngle = angle + Math.PI / 2;
            
            // 延长线的长度
            const extensionLength = 60;
            
            // 计算延长线的起点和终点
            const startX = midX - Math.cos(perpAngle) * extensionLength;
            const startY = midY - Math.sin(perpAngle) * extensionLength;
            const endX = midX + Math.cos(perpAngle) * extensionLength;
            const endY = midY + Math.sin(perpAngle) * extensionLength;

            // 绘制连接线（point0到point16）
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.setLineDash([8, 6]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(scaledPoint0.x, scaledPoint0.y);
            ctx.lineTo(scaledPoint16.x, scaledPoint16.y);
            ctx.stroke();

            // 绘制延长线
            ctx.strokeStyle = color;
            ctx.lineWidth = 2.5;
            ctx.setLineDash([6, 4]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 重置线条样式
            ctx.setLineDash([]);

            // 绘制箭头
            const arrowSize = 12;
            
            // 上箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX - arrowSize * Math.cos(perpAngle - Math.PI/6), startY - arrowSize * Math.sin(perpAngle - Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX - arrowSize * Math.cos(perpAngle + Math.PI/6), startY - arrowSize * Math.sin(perpAngle + Math.PI/6));
            ctx.stroke();
            
            // 下箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(perpAngle - Math.PI/6), endY + arrowSize * Math.sin(perpAngle - Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(perpAngle + Math.PI/6), endY + arrowSize * Math.sin(perpAngle + Math.PI/6));
            ctx.stroke();

            // 绘制距离标注
            const textX = midX + Math.cos(perpAngle) * (extensionLength + 30);
            const textY = midY + Math.sin(perpAngle) * (extensionLength + 30);

            // 绘制距离文字背景
            const text = `${faceWeight}px`;
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 16;
            const bgHeight = 24;

            // 背景阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.fillRect(textX - bgWidth/2 + 2, textY - bgHeight/2 + 2, bgWidth, bgHeight);

            // 主背景
            ctx.fillStyle = 'rgba(0, 255, 136, 0.9)';
            ctx.fillRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

            // 背景边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.globalAlpha = 0.8;
            ctx.strokeRect(textX - bgWidth/2, textY - bgHeight/2, bgWidth, bgHeight);

            // 绘制距离文字
            ctx.fillStyle = '#fff';
            ctx.globalAlpha = 1;
            ctx.fillText(text, textX, textY + 4);

            // 绘制关键点标记
            [scaledPoint0, scaledPoint16].forEach((point) => {
                // 外层发光圈
                ctx.beginPath();
                ctx.arc(point.x, point.y, 8, 0, Math.PI * 2);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.3;
                ctx.stroke();

                // 中层圈
                ctx.beginPath();
                ctx.arc(point.x, point.y, 5, 0, Math.PI * 2);
                ctx.strokeStyle = color;
                ctx.lineWidth = 1.5;
                ctx.globalAlpha = 0.6;
                ctx.stroke();

                // 主点
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
                ctx.fillStyle = color;
                ctx.globalAlpha = 1;
                ctx.fill();

                // 中心亮点
                ctx.beginPath();
                ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
                ctx.fillStyle = '#ffffff';
                ctx.globalAlpha = 0.8;
                ctx.fill();

                // 重置透明度
                ctx.globalAlpha = 1;
            });
        }

        // 绘制jaw轮廓
        function drawJawOutline(ctx, jawPoints, color = '#00E5FF') {
            if (!jawPoints || jawPoints.length === 0) return;

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.setLineDash([8, 6]);
            ctx.globalAlpha = 0.7;

            ctx.beginPath();
            const firstPoint = jawPoints[0];
            ctx.moveTo(firstPoint.x * scale + offsetX, firstPoint.y * scale + offsetY);

            for (let i = 1; i < jawPoints.length; i++) {
                const point = jawPoints[i];
                ctx.lineTo(point.x * scale + offsetX, point.y * scale + offsetY);
            }

            ctx.stroke();
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;

            // 绘制关键点
            jawPoints.forEach((point, index) => {
                const x = point.x * scale + offsetX;
                const y = point.y * scale + offsetY;
                
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fillStyle = color;
                ctx.fill();

                // 标注第0和第16个点
                if (index === 0 || index === 16) {
                    ctx.fillStyle = '#ff0000';
                    ctx.font = '12px Arial';
                    ctx.fillText(`${index}`, x + 5, y - 5);
                }
            });
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制jaw轮廓
            drawJawOutline(ctx, testData.jawPoints);

            // 绘制面部宽度
            drawFaceWeight(ctx, testData.jawPoints, testData.faceWeight);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
