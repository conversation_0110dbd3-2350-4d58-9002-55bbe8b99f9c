<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右眼高度和宽度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>右眼高度和宽度绘制测试</h1>
        <p>测试在right_eye轮廓外侧绘制平行线显示right_eye_height和right_eye_width</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div class="info">
            <h3>测试说明</h3>
            <p><strong>蓝色虚线:</strong> right_eye轮廓</p>
            <p><strong>橙色实线:</strong> right_eye_height（第38和40点）</p>
            <p><strong>绿色实线:</strong> right_eye_width（第36和39点）</p>
            <p><strong>对应箭头:</strong> 各自平行线两端的朝外箭头</p>
            <p><strong>对应文字:</strong> 各自的数值标注</p>
        </div>
    </div>

    <script>
        // 测试数据 - 模拟right_eye的landmarks
        const testData = {
            rightEyePoints: [
                { x: 150, y: 150, id: 36 }, // 右眼第1个点 - 宽度测量点1
                { x: 180, y: 140, id: 37 }, // 右眼第2个点
                { x: 220, y: 145, id: 38 }, // 右眼第3个点 - 高度测量点1
                { x: 250, y: 155, id: 39 }, // 右眼第4个点 - 宽度测量点2
                { x: 220, y: 165, id: 40 }, // 右眼第5个点 - 高度测量点2
                { x: 180, y: 160, id: 41 }  // 右眼第6个点
            ],
            rightEyeHeight: 38,
            rightEyeWidth: 115
        };

        // 绘制右眼高度的函数（简化版）
        function drawRightEyeHeight(ctx, rightEyePoints, eyeHeight) {
            console.log('🎯 开始绘制右眼高度测试');

            // 使用第38和第40个点（id=38和id=40）
            const point38 = rightEyePoints.find(p => p.id === 38);
            const point40 = rightEyePoints.find(p => p.id === 40);

            console.log('使用的点:', { point38, point40 });

            // 计算连接线的中点
            const midX = (point38.x + point40.x) / 2;
            const midY = (point38.y + point40.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point40.y - point38.y, point40.x - point38.x);
            
            // 计算平行线的偏移距离（向外偏移）
            const offsetDistance = 30;
            const perpAngle = angle + Math.PI / 2;
            
            // 计算平行线的中点位置（向外偏移）
            const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍短）
            const parallelLength = Math.sqrt(Math.pow(point40.x - point38.x, 2) + Math.pow(point40.y - point38.y, 2)) * 0.8;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point38.x, point38.y);
            ctx.lineTo(point40.x, point40.y);
            ctx.stroke();

            // 绘制平行线（橙色实线）
            ctx.strokeStyle = '#ff9d6b';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（橙色）
            const arrowSize = 10;
            ctx.strokeStyle = '#ff9d6b';
            ctx.lineWidth = 2;
            
            // 左箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（橙色背景）
            const text = `${eyeHeight}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(255, 157, 107, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point38, point40].forEach((point) => {
                ctx.fillStyle = '#ff9d6b';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 右眼高度绘制完成');
        }

        // 绘制右眼宽度的函数（简化版）
        function drawRightEyeWidth(ctx, rightEyePoints, eyeWidth) {
            console.log('🎯 开始绘制右眼宽度测试');

            // 使用第36和第39个点（id=36和id=39）
            const point36 = rightEyePoints.find(p => p.id === 36);
            const point39 = rightEyePoints.find(p => p.id === 39);

            console.log('使用的点:', { point36, point39 });

            // 计算连接线的中点
            const midX = (point36.x + point39.x) / 2;
            const midY = (point36.y + point39.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point39.y - point36.y, point39.x - point36.x);
            
            // 计算平行线的偏移距离（向外偏移）
            const offsetDistance = 35;
            const perpAngle = angle + Math.PI / 2;
            
            // 计算平行线的中点位置（向外偏移，向下偏移）
            const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍长）
            const parallelLength = Math.sqrt(Math.pow(point39.x - point36.x, 2) + Math.pow(point39.y - point36.y, 2)) * 1.1;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point36.x, point36.y);
            ctx.lineTo(point39.x, point39.y);
            ctx.stroke();

            // 绘制平行线（绿色实线）
            ctx.strokeStyle = '#6bff9d';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（绿色）
            const arrowSize = 10;
            ctx.strokeStyle = '#6bff9d';
            ctx.lineWidth = 2;
            
            // 左箭头
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（绿色背景）
            const text = `${eyeWidth}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(107, 255, 157, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point36, point39].forEach((point) => {
                ctx.fillStyle = '#6bff9d';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 右眼宽度绘制完成');
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制right_eye轮廓（蓝色虚线）
            ctx.strokeStyle = '#5F9EA0';
            ctx.lineWidth = 2;
            ctx.setLineDash([6, 4]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(testData.rightEyePoints[0].x, testData.rightEyePoints[0].y);
            for (let i = 1; i < testData.rightEyePoints.length; i++) {
                ctx.lineTo(testData.rightEyePoints[i].x, testData.rightEyePoints[i].y);
            }
            ctx.closePath();
            ctx.stroke();

            // 绘制所有right_eye点
            testData.rightEyePoints.forEach((point, index) => {
                ctx.fillStyle = '#5F9EA0';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '8px Arial';
                ctx.fillText(`${point.id}`, point.x + 5, point.y - 5);
            });

            // 绘制右眼高度
            drawRightEyeHeight(ctx, testData.rightEyePoints, testData.rightEyeHeight);

            // 绘制右眼宽度
            drawRightEyeWidth(ctx, testData.rightEyePoints, testData.rightEyeWidth);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
