<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左眼宽度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>左眼宽度绘制测试</h1>
        <p>测试在left_eye轮廓外侧绘制平行线显示left_eye_width</p>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="600" height="400"></canvas>
        </div>
        
        <div class="info">
            <h3>测试说明</h3>
            <p><strong>蓝色虚线:</strong> left_eye轮廓</p>
            <p><strong>灰色虚线:</strong> 第42和第45个点的连接线</p>
            <p><strong>紫色实线:</strong> 平行线（应该在轮廓外侧35px）</p>
            <p><strong>紫色箭头:</strong> 平行线两端的朝外箭头</p>
            <p><strong>紫色文字:</strong> left_eye_width数值标注</p>
            <p><strong>粉色线:</strong> left_eye_height（第44和46点）</p>
        </div>
    </div>

    <script>
        // 测试数据 - 模拟left_eye的landmarks
        const testData = {
            leftEyePoints: [
                { x: 200, y: 150, id: 42 }, // 左眼第1个点 - 宽度测量点1
                { x: 230, y: 140, id: 43 }, // 左眼第2个点
                { x: 270, y: 145, id: 44 }, // 左眼第3个点 - 高度测量点1
                { x: 300, y: 155, id: 45 }, // 左眼第4个点 - 宽度测量点2
                { x: 270, y: 165, id: 46 }, // 左眼第5个点 - 高度测量点2
                { x: 230, y: 160, id: 47 }  // 左眼第6个点
            ],
            leftEyeHeight: 40,
            leftEyeWidth: 115
        };

        // 绘制左眼宽度的函数（简化版）
        function drawLeftEyeWidth(ctx, leftEyePoints, eyeWidth) {
            console.log('🎯 开始绘制左眼宽度测试');
            console.log('leftEyePoints:', leftEyePoints);

            // 使用第42和第45个点（id=42和id=45）
            const point42 = leftEyePoints.find(p => p.id === 42); // id=42的点
            const point45 = leftEyePoints.find(p => p.id === 45); // id=45的点

            console.log('使用的点:', { point42, point45 });

            // 计算连接线的中点
            const midX = (point42.x + point45.x) / 2;
            const midY = (point42.y + point45.y) / 2;

            // 计算连接线的角度
            const angle = Math.atan2(point45.y - point42.y, point45.x - point42.x);
            
            // 计算平行线的偏移距离（向外偏移）
            const offsetDistance = 35;
            const perpAngle = angle + Math.PI / 2; // 垂直方向
            
            // 计算平行线的中点位置（向外偏移，向下偏移）
            const parallelMidX = midX - Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY - Math.sin(perpAngle) * offsetDistance;
            
            // 平行线的长度（比原连接线稍长）
            const parallelLength = Math.sqrt(Math.pow(point45.x - point42.x, 2) + Math.pow(point45.y - point42.y, 2)) * 1.1;
            
            // 计算平行线的起点和终点
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            console.log('📏 计算结果:');
            console.log('  连接线中点:', { x: midX, y: midY });
            console.log('  平行线中点:', { x: parallelMidX, y: parallelMidY });
            console.log('  平行线起点:', { x: startX, y: startY });
            console.log('  平行线终点:', { x: endX, y: endY });

            // 绘制连接线（灰色虚线）
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 1;
            ctx.setLineDash([4, 3]);
            ctx.globalAlpha = 0.6;
            
            ctx.beginPath();
            ctx.moveTo(point42.x, point42.y);
            ctx.lineTo(point45.x, point45.y);
            ctx.stroke();

            // 绘制平行线（紫色实线）
            ctx.strokeStyle = '#9d6bff';
            ctx.lineWidth = 2.5;
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头（紫色）
            const arrowSize = 10;
            ctx.strokeStyle = '#9d6bff';
            ctx.lineWidth = 2;
            
            // 左箭头（尖朝左）
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI + Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI + Math.PI/6));
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + arrowSize * Math.cos(angle - Math.PI - Math.PI/6), startY + arrowSize * Math.sin(angle - Math.PI - Math.PI/6));
            ctx.stroke();
            
            // 右箭头（尖朝右）
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle + Math.PI/6), endY + arrowSize * Math.sin(angle + Math.PI/6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX + arrowSize * Math.cos(angle - Math.PI/6), endY + arrowSize * Math.sin(angle - Math.PI/6));
            ctx.stroke();

            // 绘制数值标注（紫色背景）
            const text = `${eyeWidth}px`;
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 12;
            const bgHeight = 20;

            // 背景
            ctx.fillStyle = 'rgba(157, 107, 255, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.strokeRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);

            // 文字
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 3);

            // 绘制关键点
            [point42, point45].forEach((point, index) => {
                ctx.fillStyle = '#9d6bff';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText(`P${point.id}`, point.x + 6, point.y - 6);
            });

            console.log('✅ 绘制完成');
        }

        // 绘制左眼高度的函数（简化版，用于对比）
        function drawLeftEyeHeight(ctx, leftEyePoints, eyeHeight) {
            const point44 = leftEyePoints.find(p => p.id === 44);
            const point46 = leftEyePoints.find(p => p.id === 46);

            const midX = (point44.x + point46.x) / 2;
            const midY = (point44.y + point46.y) / 2;
            const angle = Math.atan2(point46.y - point44.y, point46.x - point44.x);
            
            const offsetDistance = 30;
            const perpAngle = angle + Math.PI / 2;
            const parallelMidX = midX + Math.cos(perpAngle) * offsetDistance;
            const parallelMidY = midY + Math.sin(perpAngle) * offsetDistance;
            
            const parallelLength = Math.sqrt(Math.pow(point46.x - point44.x, 2) + Math.pow(point46.y - point44.y, 2)) * 0.8;
            const startX = parallelMidX - Math.cos(angle) * parallelLength / 2;
            const startY = parallelMidY - Math.sin(angle) * parallelLength / 2;
            const endX = parallelMidX + Math.cos(angle) * parallelLength / 2;
            const endY = parallelMidY + Math.sin(angle) * parallelLength / 2;

            // 绘制高度平行线（粉色）
            ctx.strokeStyle = '#ff6b9d';
            ctx.lineWidth = 2;
            ctx.setLineDash([]);
            ctx.globalAlpha = 0.7;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制高度数值标注
            const text = `${eyeHeight}px`;
            ctx.font = 'bold 10px Arial';
            ctx.textAlign = 'center';
            const textWidth = ctx.measureText(text).width;
            const bgWidth = textWidth + 8;
            const bgHeight = 16;

            ctx.fillStyle = 'rgba(255, 107, 157, 0.9)';
            ctx.fillRect(parallelMidX - bgWidth/2, parallelMidY - bgHeight/2, bgWidth, bgHeight);
            ctx.fillStyle = '#fff';
            ctx.fillText(text, parallelMidX, parallelMidY + 2);
        }

        // 初始化canvas
        function initCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 清除canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制left_eye轮廓（蓝色虚线）
            ctx.strokeStyle = '#5F9EA0';
            ctx.lineWidth = 2;
            ctx.setLineDash([6, 4]);
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            ctx.moveTo(testData.leftEyePoints[0].x, testData.leftEyePoints[0].y);
            for (let i = 1; i < testData.leftEyePoints.length; i++) {
                ctx.lineTo(testData.leftEyePoints[i].x, testData.leftEyePoints[i].y);
            }
            ctx.closePath();
            ctx.stroke();

            // 绘制所有left_eye点
            testData.leftEyePoints.forEach((point, index) => {
                ctx.fillStyle = '#5F9EA0';
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#000';
                ctx.font = '8px Arial';
                ctx.fillText(`${point.id}`, point.x + 5, point.y - 5);
            });

            // 绘制左眼高度（用于对比）
            drawLeftEyeHeight(ctx, testData.leftEyePoints, testData.leftEyeHeight);

            // 绘制左眼宽度
            drawLeftEyeWidth(ctx, testData.leftEyePoints, testData.leftEyeWidth);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvas);
    </script>
</body>
</html>
